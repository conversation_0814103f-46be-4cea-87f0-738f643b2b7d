<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>jQuery UI Effects - Hide Demo</title>
	<link rel="stylesheet" href="../../themes/base/jquery.ui.all.css">
	<script src="../../jquery-1.9.1.js"></script>
	<script src="../../ui/jquery.ui.effect.js"></script>
	<script src="../../ui/jquery.ui.effect-blind.js"></script>
	<script src="../../ui/jquery.ui.effect-bounce.js"></script>
	<script src="../../ui/jquery.ui.effect-clip.js"></script>
	<script src="../../ui/jquery.ui.effect-drop.js"></script>
	<script src="../../ui/jquery.ui.effect-explode.js"></script>
	<script src="../../ui/jquery.ui.effect-fold.js"></script>
	<script src="../../ui/jquery.ui.effect-highlight.js"></script>
	<script src="../../ui/jquery.ui.effect-pulsate.js"></script>
	<script src="../../ui/jquery.ui.effect-scale.js"></script>
	<script src="../../ui/jquery.ui.effect-shake.js"></script>
	<script src="../../ui/jquery.ui.effect-slide.js"></script>
	<link rel="stylesheet" href="../demos.css">
	<style>
	.toggler { width: 500px; height: 200px; }
	#button { padding: .5em 1em; text-decoration: none; }
	#effect { width: 240px; height: 135px; padding: 0.4em; position: relative; }
	#effect h3 { margin: 0; padding: 0.4em; text-align: center; }
	</style>
	<script>
	$(function() {
		// run the currently selected effect
		function runEffect() {
			// get effect type from
			var selectedEffect = $( "#effectTypes" ).val();

			// most effect types need no options passed by default
			var options = {};
			// some effects have required parameters
			if ( selectedEffect === "scale" ) {
				options = { percent: 0 };
			} else if ( selectedEffect === "size" ) {
				options = { to: { width: 200, height: 60 } };
			}

			// run the effect
			$( "#effect" ).hide( selectedEffect, options, 1000, callback );
		};

		// callback function to bring a hidden box back
		function callback() {
			setTimeout(function() {
				$( "#effect" ).removeAttr( "style" ).hide().fadeIn();
			}, 1000 );
		};

		// set effect from select menu value
		$( "#button" ).click(function() {
			runEffect();
			return false;
		});
	});
	</script>
</head>
<body>

<div class="toggler">
	<div id="effect" class="ui-widget-content ui-corner-all">
		<h3 class="ui-widget-header ui-corner-all">Hide</h3>
		<p>
			Etiam libero neque, luctus a, eleifend nec, semper at, lorem. Sed pede. Nulla lorem metus, adipiscing ut, luctus sed, hendrerit vitae, mi.
		</p>
	</div>
</div>

<select name="effects" id="effectTypes">
	<option value="blind">Blind</option>
	<option value="bounce">Bounce</option>
	<option value="clip">Clip</option>
	<option value="drop">Drop</option>
	<option value="explode">Explode</option>
	<option value="fold">Fold</option>
	<option value="highlight">Highlight</option>
	<option value="puff">Puff</option>
	<option value="pulsate">Pulsate</option>
	<option value="scale">Scale</option>
	<option value="shake">Shake</option>
	<option value="size">Size</option>
	<option value="slide">Slide</option>
</select>

<a href="#" id="button" class="ui-state-default ui-corner-all">Run Effect</a>

<div class="demo-description">
<p>Click the button above to preview the effect.</p>
</div>
</body>
</html>
