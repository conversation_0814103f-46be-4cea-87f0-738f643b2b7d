<?php

namespace SAL\DLUs\DLU;
use Nette\Application\UI\Control;


class submenu extends Control{
    
    protected $id;
    protected $view;

    public function __construct($id,$view) {
        parent::__construct();
        $this->id = $id;
        $this->view = $view;
        return $this;
    }
    
    public function render() {
        $this->template->setFile(__DIR__ . '/submenu.latte');
        $this->template->id = $this->id;
        $this->template->view = $this->view;
        $this->template->render();
    }
}