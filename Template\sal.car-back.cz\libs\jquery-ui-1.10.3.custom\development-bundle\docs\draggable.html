<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>jQuery UI draggable documentation</title>

	<style>
	body {
		font-family: "Trebuchet MS", "Arial", "Helvetica", "Verdana", "sans-serif"
	}
	.gutter {
		display: none;
	}
	</style>
</head>
<body>

<script>{
		"title":
			"Draggable Widget",
		"excerpt":
			"Allow elements to be moved using the mouse.",
		"termSlugs": {
			"category": [
				"interactions"
			]
		}
	}</script><article id="draggable1" class="entry widget"><h2 class="section-title">
<span>Draggable Widget</span><span class="version-details">version added: 1.0</span>
</h2>
<div class="entry-wrapper">
<p class="desc"><strong>Description: </strong>Allow elements to be moved using the mouse.</p>
<section id="quick-nav"><header><h2>QuickNav<a href="#entry-examples">Examples</a>
</h2></header><div class="quick-nav-section">
<h3>Options</h3>
<div><a href="#option-addClasses">addClasses</a></div>
<div><a href="#option-appendTo">appendTo</a></div>
<div><a href="#option-axis">axis</a></div>
<div><a href="#option-cancel">cancel</a></div>
<div><a href="#option-connectToSortable">connectToSortable</a></div>
<div><a href="#option-containment">containment</a></div>
<div><a href="#option-cursor">cursor</a></div>
<div><a href="#option-cursorAt">cursorAt</a></div>
<div><a href="#option-delay">delay</a></div>
<div><a href="#option-disabled">disabled</a></div>
<div><a href="#option-distance">distance</a></div>
<div><a href="#option-grid">grid</a></div>
<div><a href="#option-handle">handle</a></div>
<div><a href="#option-helper">helper</a></div>
<div><a href="#option-iframeFix">iframeFix</a></div>
<div><a href="#option-opacity">opacity</a></div>
<div><a href="#option-refreshPositions">refreshPositions</a></div>
<div><a href="#option-revert">revert</a></div>
<div><a href="#option-revertDuration">revertDuration</a></div>
<div><a href="#option-scope">scope</a></div>
<div><a href="#option-scroll">scroll</a></div>
<div><a href="#option-scrollSensitivity">scrollSensitivity</a></div>
<div><a href="#option-scrollSpeed">scrollSpeed</a></div>
<div><a href="#option-snap">snap</a></div>
<div><a href="#option-snapMode">snapMode</a></div>
<div><a href="#option-snapTolerance">snapTolerance</a></div>
<div><a href="#option-stack">stack</a></div>
<div><a href="#option-zIndex">zIndex</a></div>
</div>
<div class="quick-nav-section">
<h3>Methods</h3>
<div><a href="#method-destroy">destroy</a></div>
<div><a href="#method-disable">disable</a></div>
<div><a href="#method-enable">enable</a></div>
<div><a href="#method-option">option</a></div>
<div><a href="#method-widget">widget</a></div>
</div>
<div class="quick-nav-section">
<h3>Events</h3>
<div><a href="#event-create">create</a></div>
<div><a href="#event-start">start</a></div>
<div><a href="#event-drag">drag</a></div>
<div><a href="#event-stop">stop</a></div>
</div></section><div class="longdesc" id="entry-longdesc">
		<p>Make the selected elements draggable by mouse. If you want not just drag, but drag &amp; drop, see the <a href="/droppable/">jQuery UI Droppable plugin</a>, which provides a drop target for draggables.</p>
	</div>
<section id="options"><header><h2 class="underline">Options</h2></header><div id="option-addClasses" class="api-item first-item">
<h3>addClasses<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>true</code>
</div>
<div>If set to <code>false</code>, will prevent the <code>ui-draggable</code> class from being added. This may be desired as a performance optimization when calling <code>.draggable()</code> on hundreds of elements.</div>
<strong>Code examples:</strong><p>Initialize the draggable with the addClasses option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({ addClasses: </code><code class="keyword">false</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the addClasses option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">addClasses = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"addClasses"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"addClasses"</code><code class="plain">, </code><code class="keyword">false</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-appendTo" class="api-item">
<h3>appendTo<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#jQuery">jQuery</a> or <a href="http://api.jquery.com/Types#Element">Element</a> or <a href="http://api.jquery.com/Types#Selector">Selector</a> or <a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>"parent"</code>
</div>
<div>Which element the draggable helper should be appended to while dragging.</div>
<strong>Multiple types supported:</strong><ul>
<li>
<strong>jQuery</strong>: A jQuery object containing the element to append the helper to.</li>
<li>
<strong>Element</strong>: The element to append the helper to.</li>
<li>
<strong>Selector</strong>: A selector specifying which element to append the helper to.</li>
<li>
<strong>String</strong>: The string <code>"parent"</code> will cause the helper to be a sibling of the draggable.</li>
</ul>
<strong>Code examples:</strong><p>Initialize the draggable with the appendTo option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({ appendTo: </code><code class="string">"body"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the appendTo option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">appendTo = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"appendTo"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"appendTo"</code><code class="plain">, </code><code class="string">"body"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-axis" class="api-item">
<h3>axis<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Constrains dragging to either the horizontal (x) or vertical (y) axis. Possible values: <code>"x"</code>, <code>"y"</code>.</div>
<strong>Code examples:</strong><p>Initialize the draggable with the axis option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({ axis: </code><code class="string">"x"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the axis option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">axis = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"axis"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"axis"</code><code class="plain">, </code><code class="string">"x"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-cancel" class="api-item">
<h3>cancel<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Selector">Selector</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>"input,textarea,button,select,option"</code>
</div>
<div>Prevents dragging from starting on specified elements.</div>
<strong>Code examples:</strong><p>Initialize the draggable with the cancel option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({ cancel: </code><code class="string">".title"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the cancel option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">cancel = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"cancel"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"cancel"</code><code class="plain">, </code><code class="string">".title"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-connectToSortable" class="api-item">
<h3>connectToSortable<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Selector">Selector</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Allows the draggable to be dropped onto the specified sortables. If this option is used, a draggable can be dropped onto a sortable list and then becomes part of it. Note: The <a href="#option-helper"><code>helper</code></a> option must be set to <code>"clone"</code> in order to work flawlessly. Requires the <a href="/sortable/">jQuery UI Sortable plugin</a> to be included.</div>
<strong>Code examples:</strong><p>Initialize the draggable with the connectToSortable option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({ connectToSortable: </code><code class="string">"#my-sortable"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the connectToSortable option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">connectToSortable = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"connectToSortable"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"connectToSortable"</code><code class="plain">, </code><code class="string">"#my-sortable"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-containment" class="api-item">
<h3>containment<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Selector">Selector</a> or <a href="http://api.jquery.com/Types#Element">Element</a> or <a href="http://api.jquery.com/Types#String">String</a> or <a href="http://api.jquery.com/Types#Array">Array</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Constrains dragging to within the bounds of the specified element or region.</div>
<strong>Multiple types supported:</strong><ul>
<li>
<strong>Selector</strong>: The draggable element will be contained to the bounding box of the first element found by the selector. If no element is found, no containment will be set.</li>
<li>
<strong>Element</strong>: The draggable element will be contained to the bounding box of this element.</li>
<li>
<strong>String</strong>: Possible values: <code>"parent"</code>, <code>"document"</code>, <code>"window"</code>.</li>
<li>
<strong>Array</strong>: An array defining a bounding box in the form <code>[ x1, y1, x2, y2 ]</code>.</li>
</ul>
<strong>Code examples:</strong><p>Initialize the draggable with the containment option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({ containment: </code><code class="string">"parent"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the containment option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">containment = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"containment"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"containment"</code><code class="plain">, </code><code class="string">"parent"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-cursor" class="api-item">
<h3>cursor<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>"auto"</code>
</div>
<div>The CSS cursor during the drag operation.</div>
<strong>Code examples:</strong><p>Initialize the draggable with the cursor option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({ cursor: </code><code class="string">"crosshair"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the cursor option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">cursor = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"cursor"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"cursor"</code><code class="plain">, </code><code class="string">"crosshair"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-cursorAt" class="api-item">
<h3>cursorAt<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Object">Object</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Sets the offset of the dragging helper relative to the mouse cursor. Coordinates can be given as a hash using a combination of one or two keys: <code>{ top, left, right, bottom }</code>.</div>
<strong>Code examples:</strong><p>Initialize the draggable with the cursorAt option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({ cursorAt: { left: 5 } });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the cursorAt option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">cursorAt = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"cursorAt"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"cursorAt"</code><code class="plain">, { left: 5 } );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-delay" class="api-item">
<h3>delay<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Number">Number</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>0</code>
</div>
<div>Time in milliseconds after mousedown until dragging should start. This option can be used to prevent unwanted drags when clicking on an element.</div>
<strong>Code examples:</strong><p>Initialize the draggable with the delay option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({ delay: 300 });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the delay option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">delay = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"delay"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"delay"</code><code class="plain">, 300 );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-disabled" class="api-item">
<h3>disabled<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Disables the draggable if set to <code>true</code>.</div>
<strong>Code examples:</strong><p>Initialize the draggable with the disabled option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({ disabled: </code><code class="keyword">true</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the disabled option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">disabled = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"disabled"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"disabled"</code><code class="plain">, </code><code class="keyword">true</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-distance" class="api-item">
<h3>distance<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Number">Number</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>1</code>
</div>
<div>Distance in pixels after mousedown the mouse must move before dragging should start. This option can be used to prevent unwanted drags when clicking on an element.</div>
<strong>Code examples:</strong><p>Initialize the draggable with the distance option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({ distance: 10 });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the distance option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">distance = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"distance"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"distance"</code><code class="plain">, 10 );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-grid" class="api-item">
<h3>grid<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Array">Array</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Snaps the dragging helper to a grid, every x and y pixels. The array must be of the form <code>[ x, y ]</code>.</div>
<strong>Code examples:</strong><p>Initialize the draggable with the grid option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({ grid: [ 50, 20 ] });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the grid option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">grid = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"grid"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"grid"</code><code class="plain">, [ 50, 20 ] );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-handle" class="api-item">
<h3>handle<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Selector">Selector</a> or <a href="http://api.jquery.com/Types#Element">Element</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>If specified, restricts dragging from starting unless the mousedown occurs on the specified element(s).</div>
<strong>Code examples:</strong><p>Initialize the draggable with the handle option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({ handle: </code><code class="string">"h2"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the handle option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">handle = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"handle"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"handle"</code><code class="plain">, </code><code class="string">"h2"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-helper" class="api-item">
<h3>helper<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#String">String</a> or <a href="http://api.jquery.com/Types/#Function">Function</a>()</span>
</h3>
<div class="default">
<strong>Default: </strong><code>"original"</code>
</div>
<div>Allows for a helper element to be used for dragging display.</div>
<strong>Multiple types supported:</strong><ul>
<li>
<strong>String</strong>: If set to <code>"clone"</code>, then the element will be cloned and the clone will be dragged.</li>
<li>
<strong>Function</strong>: A function that will return a DOMElement to use while dragging.</li>
</ul>
<strong>Code examples:</strong><p>Initialize the draggable with the helper option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({ helper: </code><code class="string">"clone"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the helper option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">helper = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"helper"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"helper"</code><code class="plain">, </code><code class="string">"clone"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-iframeFix" class="api-item">
<h3>iframeFix<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a> or <a href="http://api.jquery.com/Types#Selector">Selector</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Prevent iframes from capturing the mousemove events during a drag. Useful in combination with the <a href="#option-cursorAt"><code>cursorAt</code></a> option, or in any case where the mouse cursor may not be over the helper.</div>
<strong>Multiple types supported:</strong><ul>
<li>
<strong>Boolean</strong>: When set to <code>true</code>, transparent overlays will be placed over all iframes on the page.</li>
<li>
<strong>Selector</strong>: Any iframes matching the selector will be covered by transparent overlays.</li>
</ul>
<strong>Code examples:</strong><p>Initialize the draggable with the iframeFix option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({ iframeFix: </code><code class="keyword">true</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the iframeFix option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">iframeFix = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"iframeFix"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"iframeFix"</code><code class="plain">, </code><code class="keyword">true</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-opacity" class="api-item">
<h3>opacity<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Number">Number</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Opacity for the helper while being dragged.</div>
<strong>Code examples:</strong><p>Initialize the draggable with the opacity option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({ opacity: 0.35 });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the opacity option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">opacity = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"opacity"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"opacity"</code><code class="plain">, 0.35 );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-refreshPositions" class="api-item">
<h3>refreshPositions<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>
				If set to <code>true</code>, all droppable positions are calculated on every mousemove.
				<em>Caution: This solves issues on highly dynamic pages, but dramatically decreases performance.</em>
			</div>
<strong>Code examples:</strong><p>Initialize the draggable with the refreshPositions option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({ refreshPositions: </code><code class="keyword">true</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the refreshPositions option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">refreshPositions = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"refreshPositions"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"refreshPositions"</code><code class="plain">, </code><code class="keyword">true</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-revert" class="api-item">
<h3>revert<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a> or <a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Whether the element should revert to its start position when dragging stops.</div>
<strong>Multiple types supported:</strong><ul>
<li>
<strong>Boolean</strong>: If set to <code>true</code> the element will always revert.</li>
<li>
<strong>String</strong>: If set to <code>"invalid"</code>, revert will only occur if the draggable has not been dropped on a droppable. For <code>"valid"</code>, it's the other way around.</li>
</ul>
<strong>Code examples:</strong><p>Initialize the draggable with the revert option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({ revert: </code><code class="keyword">true</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the revert option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">revert = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"revert"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"revert"</code><code class="plain">, </code><code class="keyword">true</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-revertDuration" class="api-item">
<h3>revertDuration<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Number">Number</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>500</code>
</div>
<div>The duration of the revert animation, in milliseconds. Ignored if the <a href="#option-revert"><code>revert</code></a> option is <code>false</code>.</div>
<strong>Code examples:</strong><p>Initialize the draggable with the revertDuration option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({ revertDuration: 200 });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the revertDuration option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">revertDuration = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"revertDuration"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"revertDuration"</code><code class="plain">, 200 );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-scope" class="api-item">
<h3>scope<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>"default"</code>
</div>
<div>Used to group sets of draggable and droppable items, in addition to droppable's <a href="/droppable#option-accept"><code>accept</code></a> option. A draggable with the same <code>scope</code> value as a droppable will be accepted by the droppable.</div>
<strong>Code examples:</strong><p>Initialize the draggable with the scope option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({ scope: </code><code class="string">"tasks"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the scope option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">scope = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"scope"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"scope"</code><code class="plain">, </code><code class="string">"tasks"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-scroll" class="api-item">
<h3>scroll<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>true</code>
</div>
<div>If set to <code>true</code>, container auto-scrolls while dragging.</div>
<strong>Code examples:</strong><p>Initialize the draggable with the scroll option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({ scroll: </code><code class="keyword">false</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the scroll option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">scroll = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"scroll"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"scroll"</code><code class="plain">, </code><code class="keyword">false</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-scrollSensitivity" class="api-item">
<h3>scrollSensitivity<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Number">Number</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>20</code>
</div>
<div>Distance in pixels from the edge of the viewport after which the viewport should scroll. Distance is relative to pointer, not the draggable. Ignored if the <a href="#option-scroll"><code>scroll</code></a> option is <code>false</code>.</div>
<strong>Code examples:</strong><p>Initialize the draggable with the scrollSensitivity option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({ scrollSensitivity: 100 });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the scrollSensitivity option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">scrollSensitivity = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"scrollSensitivity"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"scrollSensitivity"</code><code class="plain">, 100 );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-scrollSpeed" class="api-item">
<h3>scrollSpeed<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Number">Number</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>20</code>
</div>
<div>The speed at which the window should scroll once the mouse pointer gets within the <a href="#option-scrollSensitivity"><code>scrollSensitivity</code></a> distance. Ignored if the <a href="#option-scroll"><code>scroll</code></a> option is <code>false</code>.</div>
<strong>Code examples:</strong><p>Initialize the draggable with the scrollSpeed option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({ scrollSpeed: 100 });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the scrollSpeed option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">scrollSpeed = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"scrollSpeed"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"scrollSpeed"</code><code class="plain">, 100 );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-snap" class="api-item">
<h3>snap<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a> or <a href="http://api.jquery.com/Types#Selector">Selector</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Whether the element should snap to other elements.</div>
<strong>Multiple types supported:</strong><ul>
<li>
<strong>Boolean</strong>: When set to <code>true</code>, the element will snap to all other draggable elements.</li>
<li>
<strong>Selector</strong>: A selector specifying which elements to snap to.</li>
</ul>
<strong>Code examples:</strong><p>Initialize the draggable with the snap option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({ snap: </code><code class="keyword">true</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the snap option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">snap = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"snap"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"snap"</code><code class="plain">, </code><code class="keyword">true</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-snapMode" class="api-item">
<h3>snapMode<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>"both"</code>
</div>
<div>Determines which edges of snap elements the draggable will snap to. Ignored if the <a href="#option-snap"><code>snap</code></a> option is <code>false</code>. Possible values: <code>"inner"</code>, <code>"outer"</code>, <code>"both"</code>.</div>
<strong>Code examples:</strong><p>Initialize the draggable with the snapMode option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({ snapMode: </code><code class="string">"inner"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the snapMode option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">snapMode = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"snapMode"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"snapMode"</code><code class="plain">, </code><code class="string">"inner"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-snapTolerance" class="api-item">
<h3>snapTolerance<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Number">Number</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>20</code>
</div>
<div>The distance in pixels from the snap element edges at which snapping should occur. Ignored if the <a href="#option-snap"><code>snap</code></a> option is <code>false</code>.</div>
<strong>Code examples:</strong><p>Initialize the draggable with the snapTolerance option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({ snapTolerance: 30 });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the snapTolerance option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">snapTolerance = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"snapTolerance"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"snapTolerance"</code><code class="plain">, 30 );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-stack" class="api-item">
<h3>stack<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Selector">Selector</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Controls the z-index of the set of elements that match the selector, always brings the currently dragged item to the front. Very useful in things like window managers.</div>
<strong>Code examples:</strong><p>Initialize the draggable with the stack option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({ stack: </code><code class="string">".products"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the stack option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">stack = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"stack"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"stack"</code><code class="plain">, </code><code class="string">".products"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-zIndex" class="api-item">
<h3>zIndex<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Number">Number</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Z-index for the helper while being dragged.</div>
<strong>Code examples:</strong><p>Initialize the draggable with the zIndex option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({ zIndex: 100 });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the zIndex option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">zIndex = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"zIndex"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"zIndex"</code><code class="plain">, 100 );</code></div></div></td></tr></tbody></table></div>
</div></section><section id="methods"><header><h2 class="underline">Methods</h2></header><div id="method-destroy"><div class="api-item first-item">
<h3>destroy()</h3>
<div>
		Removes the draggable functionality completely. This will return the element back to its pre-init state.
	</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the destroy method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"destroy"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-disable"><div class="api-item">
<h3>disable()</h3>
<div>
		Disables the draggable.
	</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the disable method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"disable"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-enable"><div class="api-item">
<h3>enable()</h3>
<div>
		Enables the draggable.
	</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the enable method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"enable"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-option">
<div class="api-item">
<h3>option( optionName )<span class="returns">Returns: <a href="http://api.jquery.com/Types#Object">Object</a></span>
</h3>
<div>Gets the value currently associated with the specified <code>optionName</code>.</div>
<ul><li>
<div><strong>optionName</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#String">String</a>
</div>
<div>The name of the option to get.</div>
</li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the  method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="keyword">var</code> <code class="plain">isDisabled = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"disabled"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div class="api-item">
<h3>option()<span class="returns">Returns: <a href="http://api.jquery.com/Types#PlainObject">PlainObject</a></span>
</h3>
<div>Gets an object containing key/value pairs representing the current draggable options hash.</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the  method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="keyword">var</code> <code class="plain">options = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div class="api-item">
<h3>option( optionName, value )</h3>
<div>Sets the value of the draggable option associated with the specified <code>optionName</code>.</div>
<ul>
<li>
<div><strong>optionName</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#String">String</a>
</div>
<div>The name of the option to set.</div>
</li>
<li>
<div><strong>value</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>A value to set for the option.</div>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Invoke the  method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"disabled"</code><code class="plain">, </code><code class="keyword">true</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div class="api-item">
<h3>option( options )</h3>
<div>Sets one or more options for the draggable.</div>
<ul><li>
<div><strong>options</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>A map of option-value pairs to set.</div>
</li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the  method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"option"</code><code class="plain">, { disabled: </code><code class="keyword">true</code> <code class="plain">} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
</div>
<div id="method-widget"><div class="api-item">
<h3>widget()<span class="returns">Returns: <a href="http://api.jquery.com/Types#jQuery">jQuery</a></span>
</h3>
<div>
		Returns a <code>jQuery</code> object containing the draggable element.
	</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the widget method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="keyword">var</code> <code class="plain">widget = $( </code><code class="string">".selector"</code> <code class="plain">).draggable( </code><code class="string">"widget"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div></section><section id="events"><header><h2 class="underline">Events</h2></header><div id="event-create" class="api-item first-item">
<h3>create( event, ui )<span class="returns">Type: <code>dragcreate</code></span>
</h3>
<div>
		Triggered when the draggable is created.
	</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the draggable with the create callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">create: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the dragcreate event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"dragcreate"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div id="event-drag" class="api-item">
<h3>drag( event, ui )<span class="returns">Type: <code>drag</code></span>
</h3>
<div>Triggered while the mouse is moved during the dragging.</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
<ul>
<li>
<div><strong>helper</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The jQuery object representing the helper that's being dragged.</div>
</li>
<li>
<div><strong>position</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>Current CSS position of the helper as <code>{ top, left }</code> object.</div>
</li>
<li>
<div><strong>offset</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>Current offset position of the helper as <code>{ top, left }</code> object.</div>
</li>
</ul>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the draggable with the drag callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">drag: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the drag event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"drag"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div id="event-start" class="api-item">
<h3>start( event, ui )<span class="returns">Type: <code>dragstart</code></span>
</h3>
<div>Triggered when dragging starts.</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
<ul>
<li>
<div><strong>helper</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The jQuery object representing the helper that's being dragged.</div>
</li>
<li>
<div><strong>position</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>Current CSS position of the helper as <code>{ top, left }</code> object.</div>
</li>
<li>
<div><strong>offset</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>Current offset position of the helper as <code>{ top, left }</code> object.</div>
</li>
</ul>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the draggable with the start callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">start: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the dragstart event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"dragstart"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div id="event-stop" class="api-item">
<h3>stop( event, ui )<span class="returns">Type: <code>dragstop</code></span>
</h3>
<div>Triggered when dragging stops.</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
<ul>
<li>
<div><strong>helper</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The jQuery object representing the helper that's being dragged.</div>
</li>
<li>
<div><strong>position</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>Current CSS position of the helper as <code>{ top, left }</code> object.</div>
</li>
<li>
<div><strong>offset</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>Current offset position of the helper as <code>{ top, left }</code> object.</div>
</li>
</ul>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the draggable with the stop callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).draggable({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">stop: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the dragstop event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"dragstop"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div></section><section class="entry-examples" id="entry-examples"><header><h2 class="underline">Example:</h2></header><div class="entry-example" id="example-0">
<h4><span class="desc">A simple jQuery UI Draggable</span></h4>
<div class="syntaxhighlighter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="gutter"><div class="line number1 index0 alt2">1</div><div class="line number2 index1 alt1">2</div><div class="line number3 index2 alt2">3</div><div class="line number4 index3 alt1">4</div><div class="line number5 index4 alt2">5</div><div class="line number6 index5 alt1">6</div><div class="line number7 index6 alt2">7</div><div class="line number8 index7 alt1">8</div><div class="line number9 index8 alt2">9</div><div class="line number10 index9 alt1">10</div><div class="line number11 index10 alt2">11</div><div class="line number12 index11 alt1">12</div><div class="line number13 index12 alt2">13</div><div class="line number14 index13 alt1">14</div><div class="line number15 index14 alt2">15</div><div class="line number16 index15 alt1">16</div><div class="line number17 index16 alt2">17</div><div class="line number18 index17 alt1">18</div><div class="line number19 index18 alt2">19</div><div class="line number20 index19 alt1">20</div><div class="line number21 index20 alt2">21</div><div class="line number22 index21 alt1">22</div><div class="line number23 index22 alt2">23</div><div class="line number24 index23 alt1">24</div><div class="line number25 index24 alt2">25</div><div class="line number26 index25 alt1">26</div></td><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">&lt;!doctype html&gt;</code></div><div class="line number2 index1 alt1"><code class="plain">&lt;</code><code class="keyword">html</code> <code class="color1">lang</code><code class="plain">=</code><code class="string">"en"</code><code class="plain">&gt;</code></div><div class="line number3 index2 alt2"><code class="plain">&lt;</code><code class="keyword">head</code><code class="plain">&gt;</code></div><div class="line number4 index3 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">meta</code> <code class="color1">charset</code><code class="plain">=</code><code class="string">"utf-8"</code><code class="plain">&gt;</code></div><div class="line number5 index4 alt2"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">title</code><code class="plain">&gt;draggable demo&lt;/</code><code class="keyword">title</code><code class="plain">&gt;</code></div><div class="line number6 index5 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">link</code> <code class="color1">rel</code><code class="plain">=</code><code class="string">"stylesheet"</code> <code class="color1">href</code><code class="plain">=</code><code class="string">"<a href="http://code.jquery.com/ui/1.9.2/themes/base/jquery-ui.css">http://code.jquery.com/ui/1.9.2/themes/base/jquery-ui.css</a>"</code><code class="plain">&gt;</code></div><div class="line number7 index6 alt2"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">style</code><code class="plain">&gt;</code></div><div class="line number8 index7 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">#draggable {</code></div><div class="line number9 index8 alt2"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">width: 100px;</code></div><div class="line number10 index9 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">height: 100px;</code></div><div class="line number11 index10 alt2"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">background: #ccc;</code></div><div class="line number12 index11 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">}</code></div><div class="line number13 index12 alt2"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;/</code><code class="keyword">style</code><code class="plain">&gt;</code></div><div class="line number14 index13 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">script</code> <code class="color1">src</code><code class="plain">=</code><code class="string">"<a href="http://code.jquery.com/jquery-1.8.3.js">http://code.jquery.com/jquery-1.8.3.js</a>"</code><code class="plain">&gt;&lt;/</code><code class="keyword">script</code><code class="plain">&gt;</code></div><div class="line number15 index14 alt2"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">script</code> <code class="color1">src</code><code class="plain">=</code><code class="string">"<a href="http://code.jquery.com/ui/1.9.2/jquery-ui.js">http://code.jquery.com/ui/1.9.2/jquery-ui.js</a>"</code><code class="plain">&gt;&lt;/</code><code class="keyword">script</code><code class="plain">&gt;</code></div><div class="line number16 index15 alt1"><code class="plain">&lt;/</code><code class="keyword">head</code><code class="plain">&gt;</code></div><div class="line number17 index16 alt2"><code class="plain">&lt;</code><code class="keyword">body</code><code class="plain">&gt;</code></div><div class="line number18 index17 alt1">&nbsp;</div><div class="line number19 index18 alt2"><code class="plain">&lt;</code><code class="keyword">div</code> <code class="color1">id</code><code class="plain">=</code><code class="string">"draggable"</code><code class="plain">&gt;Drag me&lt;/</code><code class="keyword">div</code><code class="plain">&gt;</code></div><div class="line number20 index19 alt1">&nbsp;</div><div class="line number21 index20 alt2"><code class="plain">&lt;</code><code class="keyword">script</code><code class="plain">&gt;</code></div><div class="line number22 index21 alt1"><code class="plain">$( "#draggable" ).draggable();</code></div><div class="line number23 index22 alt2"><code class="plain">&lt;/</code><code class="keyword">script</code><code class="plain">&gt;</code></div><div class="line number24 index23 alt1">&nbsp;</div><div class="line number25 index24 alt2"><code class="plain">&lt;/</code><code class="keyword">body</code><code class="plain">&gt;</code></div><div class="line number26 index25 alt1"><code class="plain">&lt;/</code><code class="keyword">html</code><code class="plain">&gt;</code></div></div></td></tr></tbody></table></div>
<h4>Demo:</h4>
<div class="demo code-demo"></div>
</div></section>
</div></article>

</body>
</html>
