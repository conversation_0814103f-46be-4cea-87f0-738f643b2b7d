using DebtDesk.Core.Entities;
using DebtDesk.Core.Enums;

namespace DebtDesk.Core.Interfaces
{
    public interface IDebtCaseRepository : IRepository<DebtCase>
    {
        Task<IEnumerable<DebtCase>> GetNewDebtCasesAsync();
        Task<IEnumerable<DebtCase>> GetNewRUCasesAsync();
        Task<IEnumerable<DebtCase>> GetNewKVCasesAsync();
        Task<IEnumerable<DebtCase>> GetImportedCasesAsync();
        Task<IEnumerable<DebtCase>> GetCasesForDivisionAsync();
        Task<IEnumerable<DebtCase>> GetOpenCasesAsync();
        Task<IEnumerable<DebtCase>> GetOpenCasesByUserAsync(int userId);
        Task<IEnumerable<DebtCase>> GetClosedCasesAsync();
        Task<IEnumerable<DebtCase>> GetCasesByStatusAsync(DebtCaseStatus status);
        Task UpdateDebtCaseAsync(int id, Dictionary<string, object> updates);
        Task<DebtCase?> GetCaseByEvCisloAsync(string evCislo);
    }
}
