using Microsoft.EntityFrameworkCore;
using DebtDesk.Core.Entities;

namespace DebtDesk.Infrastructure.Data
{
    public class DebtDeskDbContext : DbContext
    {
        public DebtDeskDbContext(DbContextOptions<DebtDeskDbContext> options) : base(options)
        {
        }

        public DbSet<User> Users { get; set; }
        public DbSet<Core.Entities.File> Files { get; set; }
        public DbSet<DebtCase> DebtCases { get; set; }
        public DbSet<DebtType> DebtTypes { get; set; }
        public DbSet<PaymentSchedule> PaymentSchedules { get; set; }
        public DbSet<Payment> Payments { get; set; }
        public DbSet<DebtCaseDocument> DebtCaseDocuments { get; set; }
        public DbSet<Photo> Photos { get; set; }
        public DbSet<Review> Reviews { get; set; }
        public DbSet<ReviewDocument> ReviewDocuments { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure table names to match existing database
            modelBuilder.Entity<User>().ToTable("user");
            modelBuilder.Entity<Core.Entities.File>().ToTable("file");
            modelBuilder.Entity<DebtCase>().ToTable("dlu");
            modelBuilder.Entity<DebtType>().ToTable("dlutype");
            modelBuilder.Entity<PaymentSchedule>().ToTable("sk");
            modelBuilder.Entity<Payment>().ToTable("uhr");
            modelBuilder.Entity<DebtCaseDocument>().ToTable("dludocs");
            modelBuilder.Entity<Photo>().ToTable("photo");
            modelBuilder.Entity<Review>().ToTable("review");
            modelBuilder.Entity<ReviewDocument>().ToTable("reviewdoc");

            // Configure unique constraints
            modelBuilder.Entity<Core.Entities.File>()
                .HasIndex(f => f.Name)
                .IsUnique();

            // Configure foreign key relationships
            modelBuilder.Entity<DebtCase>()
                .HasOne(d => d.File)
                .WithMany(f => f.DebtCases)
                .HasForeignKey(d => d.FileId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<DebtCase>()
                .HasOne(d => d.DebtType)
                .WithMany(dt => dt.DebtCases)
                .HasForeignKey(d => d.DebtTypeId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<DebtCase>()
                .HasOne(d => d.User)
                .WithMany(u => u.DebtCases)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<PaymentSchedule>()
                .HasOne(ps => ps.File)
                .WithMany(f => f.PaymentSchedules)
                .HasForeignKey(ps => ps.FileId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Payment>()
                .HasOne(p => p.File)
                .WithMany(f => f.Payments)
                .HasForeignKey(p => p.FileId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<DebtCaseDocument>()
                .HasOne(d => d.DebtCase)
                .WithMany(dc => dc.Documents)
                .HasForeignKey(d => d.DebtCaseId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Photo>()
                .HasOne(p => p.DebtCase)
                .WithMany(dc => dc.Photos)
                .HasForeignKey(p => p.DebtCaseId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Review>()
                .HasOne(r => r.File)
                .WithMany(f => f.Reviews)
                .HasForeignKey(r => r.FileId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<ReviewDocument>()
                .HasOne(rd => rd.Review)
                .WithMany(r => r.Documents)
                .HasForeignKey(rd => rd.ReviewId)
                .OnDelete(DeleteBehavior.Cascade);

            // Configure indexes for performance
            modelBuilder.Entity<DebtCase>()
                .HasIndex(d => d.DluEvCislo);

            modelBuilder.Entity<PaymentSchedule>()
                .HasIndex(ps => ps.DluEvCislo);

            modelBuilder.Entity<Payment>()
                .HasIndex(p => p.DluEvCislo);

            // Configure enum conversion
            modelBuilder.Entity<Review>()
                .Property(r => r.Type)
                .HasConversion<string>();
        }
    }
}
