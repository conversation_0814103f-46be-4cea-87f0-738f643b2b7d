<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>jQuery UI Menu - Icons</title>
	<link rel="stylesheet" href="../../themes/base/jquery.ui.all.css">
	<script src="../../jquery-1.9.1.js"></script>
	<script src="../../ui/jquery.ui.core.js"></script>
	<script src="../../ui/jquery.ui.widget.js"></script>
	<script src="../../ui/jquery.ui.position.js"></script>
	<script src="../../ui/jquery.ui.menu.js"></script>
	<link rel="stylesheet" href="../demos.css">
	<script>
	$(function() {
		$( "#menu" ).menu();
	});
	</script>
	<style>
	.ui-menu { width: 150px; }
	</style>
</head>
<body>

<ul id="menu">
	<li><a href="#"><span class="ui-icon ui-icon-disk"></span>Save</a></li>
	<li><a href="#"><span class="ui-icon ui-icon-zoomin"></span>Zoom In</a></li>
	<li><a href="#"><span class="ui-icon ui-icon-zoomout"></span>Zoom Out</a></li>
	<li class="ui-state-disabled"><a href="#"><span class="ui-icon ui-icon-print"></span>Print...</a></li>
	<li>
		<a href="#">Playback</a>
		<ul>
			<li><a href="#"><span class="ui-icon ui-icon-seek-start"></span>Prev</a></li>
			<li><a href="#"><span class="ui-icon ui-icon-stop"></span>Stop</a></li>
			<li><a href="#"><span class="ui-icon ui-icon-play"></span>Play</a></li>
			<li><a href="#"><span class="ui-icon ui-icon-seek-end"></span>Next</a></li>
		</ul>
	</li>
</ul>

<div class="demo-description">
<p>A menu with the default configuration, showing how to use a menu with icons.</p>
</div>
</body>
</html>
