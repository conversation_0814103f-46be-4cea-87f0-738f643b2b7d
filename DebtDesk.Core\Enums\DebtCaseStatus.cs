namespace DebtDesk.Core.Enums
{
    /// <summary>
    /// Debt case status enumeration
    /// </summary>
    public enum DebtCaseStatus : byte
    {
        /// <summary>
        /// Import - Loaded into DB
        /// </summary>
        Import = 0,

        /// <summary>
        /// Mail - Notification sent about new cases
        /// </summary>
        Mail = 10,

        /// <summary>
        /// Division - Assignment date and collection type recorded
        /// </summary>
        Division = 20,

        /// <summary>
        /// Open - Assigned to collector
        /// </summary>
        Open = 30,

        /// <summary>
        /// Control - Case under review
        /// </summary>
        Control = 40,

        /// <summary>
        /// Close - Closed case
        /// </summary>
        Close = 90,

        /// <summary>
        /// Delete - Deleted case
        /// </summary>
        Delete = 99
    }
}
