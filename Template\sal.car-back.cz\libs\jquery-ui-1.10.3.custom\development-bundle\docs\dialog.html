<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>jQuery UI dialog documentation</title>

	<style>
	body {
		font-family: "Trebuchet MS", "Arial", "Helvetica", "Verdana", "sans-serif"
	}
	.gutter {
		display: none;
	}
	</style>
</head>
<body>

<script>{
		"title":
			"Dialog Widget",
		"excerpt":
			"Open content in an interactive overlay.",
		"termSlugs": {
			"category": [
				"widgets"
			]
		}
	}</script><article id="dialog1" class="entry widget"><h2 class="section-title">
<span>Dialog Widget</span><span class="version-details">version added: 1.0</span>
</h2>
<div class="entry-wrapper">
<p class="desc"><strong>Description: </strong>Open content in an interactive overlay.</p>
<section id="quick-nav"><header><h2>QuickNav<a href="#entry-examples">Examples</a>
</h2></header><div class="quick-nav-section">
<h3>Options</h3>
<div><a href="#option-appendTo">appendTo</a></div>
<div><a href="#option-autoOpen">autoOpen</a></div>
<div><a href="#option-buttons">buttons</a></div>
<div><a href="#option-closeOnEscape">closeOnEscape</a></div>
<div><a href="#option-closeText">closeText</a></div>
<div><a href="#option-dialogClass">dialogClass</a></div>
<div><a href="#option-draggable">draggable</a></div>
<div><a href="#option-height">height</a></div>
<div><a href="#option-hide">hide</a></div>
<div><a href="#option-maxHeight">maxHeight</a></div>
<div><a href="#option-maxWidth">maxWidth</a></div>
<div><a href="#option-minHeight">minHeight</a></div>
<div><a href="#option-minWidth">minWidth</a></div>
<div><a href="#option-modal">modal</a></div>
<div><a href="#option-position">position</a></div>
<div><a href="#option-resizable">resizable</a></div>
<div><a href="#option-show">show</a></div>
<div><a href="#option-title">title</a></div>
<div><a href="#option-width">width</a></div>
</div>
<div class="quick-nav-section">
<h3>Methods</h3>
<div><a href="#method-close">close</a></div>
<div><a href="#method-destroy">destroy</a></div>
<div><a href="#method-isOpen">isOpen</a></div>
<div><a href="#method-moveToTop">moveToTop</a></div>
<div><a href="#method-open">open</a></div>
<div><a href="#method-option">option</a></div>
<div><a href="#method-widget">widget</a></div>
</div>
<div class="quick-nav-section">
<h3>Events</h3>
<div><a href="#event-beforeClose">beforeClose</a></div>
<div><a href="#event-create">create</a></div>
<div><a href="#event-open">open</a></div>
<div><a href="#event-focus">focus</a></div>
<div><a href="#event-dragStart">dragStart</a></div>
<div><a href="#event-drag">drag</a></div>
<div><a href="#event-dragStop">dragStop</a></div>
<div><a href="#event-resizeStart">resizeStart</a></div>
<div><a href="#event-resize">resize</a></div>
<div><a href="#event-resizeStop">resizeStop</a></div>
<div><a href="#event-close">close</a></div>
</div></section><div class="longdesc" id="entry-longdesc">
		<p>A dialog is a floating window that contains a title bar and a content area. The dialog window can be moved, resized and closed with the 'x' icon by default.</p>

		<p>If the content length exceeds the maximum height, a scrollbar will automatically appear.</p>

		<p>A bottom button bar and semi-transparent modal overlay layer are common options that can be added.</p>
	</div>
<h3>Additional Notes:</h3>
<div class="longdesc"><ul><li>
			This widget requires some functional CSS, otherwise it won't work. If you build a custom theme, use the widget's specific CSS file as a starting point.
		</li></ul></div>
<section id="options"><header><h2 class="underline">Options</h2></header><div id="option-appendTo" class="api-item first-item">
<h3>appendTo<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Selector">Selector</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>"body"</code>
</div>
<div>Which element the dialog should be appended to. Regardless of the value set, the overlay for modal dialogs will always be appended to the body and cover the entire window.<span class="version-details"> (version added: 1.10)</span>
</div>
<strong>Code examples:</strong><p>Initialize the dialog with the appendTo option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({ appendTo: </code><code class="string">"#someElem"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the appendTo option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">appendTo = $( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"appendTo"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"appendTo"</code><code class="plain">, </code><code class="string">"#someElem"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-autoOpen" class="api-item">
<h3>autoOpen<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>true</code>
</div>
<div>If set to <code>true</code>, the dialog will automatically open upon initialization. If <code>false</code>, the dialog will stay hidden until the <a href="#method-open"><code>open()</code></a> method is called.</div>
<strong>Code examples:</strong><p>Initialize the dialog with the autoOpen option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({ autoOpen: </code><code class="keyword">false</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the autoOpen option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">autoOpen = $( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"autoOpen"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"autoOpen"</code><code class="plain">, </code><code class="keyword">false</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-buttons" class="api-item">
<h3>buttons<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Object">Object</a> or <a href="http://api.jquery.com/Types#Array">Array</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>{}</code>
</div>
<div>Specifies which buttons should be displayed on the dialog. The context of the callback is the dialog element; if you need access to the button, it is available as the target of the event object.</div>
<strong>Multiple types supported:</strong><ul>
<li>
<strong>Object</strong>: The keys are the button labels and the values are the callbacks for when the associated button is clicked.</li>
<li>
<strong>Array</strong>: Each element of the array must be an object defining the attributes, properties, and event handlers to set on the button.</li>
</ul>
<strong>Code examples:</strong><p>Initialize the dialog with the buttons option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({ buttons: [ { text: </code><code class="string">"Ok"</code><code class="plain">, click: </code><code class="keyword">function</code><code class="plain">() { $( </code><code class="keyword">this</code> <code class="plain">).dialog( </code><code class="string">"close"</code> <code class="plain">); } ] });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the buttons option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">buttons = $( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"buttons"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"buttons"</code><code class="plain">, [ { text: </code><code class="string">"Ok"</code><code class="plain">, click: </code><code class="keyword">function</code><code class="plain">() { $( </code><code class="keyword">this</code> <code class="plain">).dialog( </code><code class="string">"close"</code> <code class="plain">); } ] );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-closeOnEscape" class="api-item">
<h3>closeOnEscape<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>true</code>
</div>
<div>Specifies whether the dialog should close when it has focus and the user presses the esacpe (ESC) key.</div>
<strong>Code examples:</strong><p>Initialize the dialog with the closeOnEscape option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({ closeOnEscape: </code><code class="keyword">false</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the closeOnEscape option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">closeOnEscape = $( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"closeOnEscape"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"closeOnEscape"</code><code class="plain">, </code><code class="keyword">false</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-closeText" class="api-item">
<h3>closeText<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>"close"</code>
</div>
<div>Specifies the text for the close button. Note that the close text is visibly hidden when using a standard theme.</div>
<strong>Code examples:</strong><p>Initialize the dialog with the closeText option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({ closeText: </code><code class="string">"hide"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the closeText option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">closeText = $( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"closeText"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"closeText"</code><code class="plain">, </code><code class="string">"hide"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-dialogClass" class="api-item">
<h3>dialogClass<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>""</code>
</div>
<div>The specified class name(s) will be added to the dialog, for additional theming.</div>
<strong>Code examples:</strong><p>Initialize the dialog with the dialogClass option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({ dialogClass: </code><code class="string">"alert"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the dialogClass option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">dialogClass = $( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"dialogClass"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"dialogClass"</code><code class="plain">, </code><code class="string">"alert"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-draggable" class="api-item">
<h3>draggable<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>true</code>
</div>
<div>If set to <code>true</code>, the dialog will be draggable by the title bar. Requires the <a href="/draggable/">jQuery UI Draggable wiget</a> to be included.</div>
<strong>Code examples:</strong><p>Initialize the dialog with the draggable option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({ draggable: </code><code class="keyword">false</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the draggable option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">draggable = $( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"draggable"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"draggable"</code><code class="plain">, </code><code class="keyword">false</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-height" class="api-item">
<h3>height<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Number">Number</a> or <a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>"auto"</code>
</div>
<div>The height of the dialog.</div>
<strong>Multiple types supported:</strong><ul>
<li>
<strong>Number</strong>: The height in pixels.</li>
<li>
<strong>String</strong>: The only supported string value is <code>"auto"</code> which will allow the dialog height to adjust based on its content.</li>
</ul>
<strong>Code examples:</strong><p>Initialize the dialog with the height option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({ height: 400 });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the height option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">height = $( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"height"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"height"</code><code class="plain">, 400 );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-hide" class="api-item">
<h3>hide<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Number">Number</a> or <a href="http://api.jquery.com/Types#String">String</a> or <a href="http://api.jquery.com/Types#Object">Object</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>null</code>
</div>
<div>If and how to animate the hiding of the dialog.</div>
<strong>Multiple types supported:</strong><ul>
<li>
<strong>Number</strong>: 
					The dialog will fade out while animating the height and width for the specified duration.
				</li>
<li>
<strong>String</strong>: 
					The dialog will be hidden using the specified jQuery UI effect. See the <a href="/category/effects/">list of effects</a> for possible values.
				</li>
<li>
<strong>Object</strong>: If the value is an object, then <code>effect</code>, <code>duration</code>, and <code>easing</code> properties may be provided. The <code>effect</code> property must be the name of a jQuery UI effect. When using a jQuery UI effect that supports additional settings, you may include those settings in the object and they will be passed to the effect. If <code>duration</code> or <code>easing</code> is omitted, then the default values will be used.</li>
</ul>
<strong>Code examples:</strong><p>Initialize the dialog with the hide option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({ hide: </code><code class="string">"explode"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the hide option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">hide = $( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"hide"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"hide"</code><code class="plain">, </code><code class="string">"explode"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-maxHeight" class="api-item">
<h3>maxHeight<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Number">Number</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>The maximum height to which the dialog can be resized, in pixels.</div>
<strong>Code examples:</strong><p>Initialize the dialog with the maxHeight option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({ maxHeight: 600 });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the maxHeight option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">maxHeight = $( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"maxHeight"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"maxHeight"</code><code class="plain">, 600 );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-maxWidth" class="api-item">
<h3>maxWidth<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Number">Number</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>The maximum width to which the dialog can be resized, in pixels.</div>
<strong>Code examples:</strong><p>Initialize the dialog with the maxWidth option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({ maxWidth: 600 });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the maxWidth option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">maxWidth = $( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"maxWidth"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"maxWidth"</code><code class="plain">, 600 );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-minHeight" class="api-item">
<h3>minHeight<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Number">Number</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>150</code>
</div>
<div>The minimum height to which the dialog can be resized, in pixels.</div>
<strong>Code examples:</strong><p>Initialize the dialog with the minHeight option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({ minHeight: 200 });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the minHeight option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">minHeight = $( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"minHeight"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"minHeight"</code><code class="plain">, 200 );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-minWidth" class="api-item">
<h3>minWidth<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Number">Number</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>150</code>
</div>
<div>The minimum width to which the dialog can be resized, in pixels.</div>
<strong>Code examples:</strong><p>Initialize the dialog with the minWidth option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({ minWidth: 200 });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the minWidth option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">minWidth = $( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"minWidth"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"minWidth"</code><code class="plain">, 200 );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-modal" class="api-item">
<h3>modal<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>If set to <code>true</code>, the dialog will have modal behavior; other items on the page will be disabled, i.e., cannot be interacted with. Modal dialogs create an overlay below the dialog but above other page elements.</div>
<strong>Code examples:</strong><p>Initialize the dialog with the modal option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({ modal: </code><code class="keyword">true</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the modal option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">modal = $( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"modal"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"modal"</code><code class="plain">, </code><code class="keyword">true</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-position" class="api-item">
<h3>position<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Object">Object</a> or <a href="http://api.jquery.com/Types#String">String</a> or <a href="http://api.jquery.com/Types#Array">Array</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>{ my: "center", at: "center", of: window }</code>
</div>
<div>
				<p>Specifies where the dialog should be displayed. The dialog will handle collisions such that as much of the dialog is visible as possible.</p>
				<p><em>Note: The <code>String</code> and <code>Array</code> forms are deprecated.</em></p>
			</div>
<strong>Multiple types supported:</strong><ul>
<li>
<strong>Object</strong>: Identifies the position of the dialog when opened. The <code>of</code> option defaults to the window, but you can specify another element to position against. You can refer to the <a href="/position">jQuery UI Position</a> utility for more details about the various options.</li>
<li>
<strong>String</strong>: A string representing the position within the viewport. Possible values: <code>"center"</code>, <code>"left"</code>, <code>"right"</code>, <code>"top"</code>, <code>"bottom"</code>.</li>
<li>
<strong>Array</strong>: An array containing an <em>x, y</em> coordinate pair in pixel offset from the top left corner of the viewport or the name of a possible string value.</li>
</ul>
<strong>Code examples:</strong><p>Initialize the dialog with the position option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({ position: { my: </code><code class="string">"left top"</code><code class="plain">, at: </code><code class="string">"left bottom"</code><code class="plain">, of: button } });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the position option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">position = $( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"position"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"position"</code><code class="plain">, { my: </code><code class="string">"left top"</code><code class="plain">, at: </code><code class="string">"left bottom"</code><code class="plain">, of: button } );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-resizable" class="api-item">
<h3>resizable<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>true</code>
</div>
<div>If set to <code>true</code>, the dialog will be resizable. Requires the <a href="/resizable/">jQuery UI Resizable widget</a> to be included.</div>
<strong>Code examples:</strong><p>Initialize the dialog with the resizable option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({ resizable: </code><code class="keyword">false</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the resizable option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">resizable = $( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"resizable"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"resizable"</code><code class="plain">, </code><code class="keyword">false</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-show" class="api-item">
<h3>show<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Number">Number</a> or <a href="http://api.jquery.com/Types#String">String</a> or <a href="http://api.jquery.com/Types#Object">Object</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>null</code>
</div>
<div>If and how to animate the showing of the dialog.</div>
<strong>Multiple types supported:</strong><ul>
<li>
<strong>Number</strong>: 
					The dialog will fade in while animating the height and width for the specified duration.
				</li>
<li>
<strong>String</strong>: 
					The dialog will be shown using the specified jQuery UI effect. See the <a href="/category/effects/">list of effects</a> for possible values.
				</li>
<li>
<strong>Object</strong>: If the value is an object, then <code>effect</code>, <code>duration</code>, and <code>easing</code> properties may be provided. The <code>effect</code> property must be the name of a jQuery UI effect. When using a jQuery UI effect that supports additional settings, you may include those settings in the object and they will be passed to the effect. If <code>duration</code> or <code>easing</code> is omitted, then the default values will be used.</li>
</ul>
<strong>Code examples:</strong><p>Initialize the dialog with the show option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({ show: </code><code class="string">"slow"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the show option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">show = $( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"show"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"show"</code><code class="plain">, </code><code class="string">"slow"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-title" class="api-item">
<h3>title<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>null</code>
</div>
<div>Specifies the title of the dialog. Any valid HTML may be set as the title. The title can also be specified by the <code>title</code> attribute on the dialog source element.</div>
<strong>Code examples:</strong><p>Initialize the dialog with the title option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({ title: </code><code class="string">"Dialog Title"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the title option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">title = $( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"title"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"title"</code><code class="plain">, </code><code class="string">"Dialog Title"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-width" class="api-item">
<h3>width<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Number">Number</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>300</code>
</div>
<div>The width of the dialog, in pixels.</div>
<strong>Code examples:</strong><p>Initialize the dialog with the width option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({ width: 500 });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the width option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">width = $( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"width"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"width"</code><code class="plain">, 500 );</code></div></div></td></tr></tbody></table></div>
</div></section><section id="methods"><header><h2 class="underline">Methods</h2></header><div id="method-close"><div class="api-item first-item">
<h3>close()</h3>
<div>Closes the dialog.</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the close method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"close"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-destroy"><div class="api-item">
<h3>destroy()</h3>
<div>
		Removes the dialog functionality completely. This will return the element back to its pre-init state.
	</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the destroy method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"destroy"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-isOpen"><div class="api-item">
<h3>isOpen()<span class="returns">Returns: <a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div>Whether the dialog is currently open.</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the isOpen method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="keyword">var</code> <code class="plain">isOpen = $( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"isOpen"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-moveToTop"><div class="api-item">
<h3>moveToTop()</h3>
<div>Moves the dialog to the top of the dialog stack.</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the moveToTop method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"moveToTop"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-open"><div class="api-item">
<h3>open()</h3>
<div>Opens the dialog.</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the open method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"open"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-option">
<div class="api-item">
<h3>option( optionName )<span class="returns">Returns: <a href="http://api.jquery.com/Types#Object">Object</a></span>
</h3>
<div>Gets the value currently associated with the specified <code>optionName</code>.</div>
<ul><li>
<div><strong>optionName</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#String">String</a>
</div>
<div>The name of the option to get.</div>
</li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the  method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="keyword">var</code> <code class="plain">isDisabled = $( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"disabled"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div class="api-item">
<h3>option()<span class="returns">Returns: <a href="http://api.jquery.com/Types#PlainObject">PlainObject</a></span>
</h3>
<div>Gets an object containing key/value pairs representing the current dialog options hash.</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the  method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="keyword">var</code> <code class="plain">options = $( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div class="api-item">
<h3>option( optionName, value )</h3>
<div>Sets the value of the dialog option associated with the specified <code>optionName</code>.</div>
<ul>
<li>
<div><strong>optionName</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#String">String</a>
</div>
<div>The name of the option to set.</div>
</li>
<li>
<div><strong>value</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>A value to set for the option.</div>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Invoke the  method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"disabled"</code><code class="plain">, </code><code class="keyword">true</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div class="api-item">
<h3>option( options )</h3>
<div>Sets one or more options for the dialog.</div>
<ul><li>
<div><strong>options</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>A map of option-value pairs to set.</div>
</li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the  method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"option"</code><code class="plain">, { disabled: </code><code class="keyword">true</code> <code class="plain">} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
</div>
<div id="method-widget"><div class="api-item">
<h3>widget()<span class="returns">Returns: <a href="http://api.jquery.com/Types#jQuery">jQuery</a></span>
</h3>
<div>
		Returns a <code>jQuery</code> object containing the generated wrapper.
	</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the widget method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="keyword">var</code> <code class="plain">widget = $( </code><code class="string">".selector"</code> <code class="plain">).dialog( </code><code class="string">"widget"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div></section><section id="events"><header><h2 class="underline">Events</h2></header><div id="event-beforeClose" class="api-item first-item">
<h3>beforeClose( event, ui )<span class="returns">Type: <code>dialogbeforeclose</code></span>
</h3>
<div>Triggered when a dialog is about to close. If canceled, the dialog will not close.</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the dialog with the beforeClose callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">beforeClose: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the dialogbeforeclose event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"dialogbeforeclose"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div id="event-close" class="api-item">
<h3>close( event, ui )<span class="returns">Type: <code>dialogclose</code></span>
</h3>
<div>Triggered when the dialog is closed.</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the dialog with the close callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">close: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the dialogclose event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"dialogclose"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div id="event-create" class="api-item">
<h3>create( event, ui )<span class="returns">Type: <code>dialogcreate</code></span>
</h3>
<div>
		Triggered when the dialog is created.
	</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the dialog with the create callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">create: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the dialogcreate event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"dialogcreate"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div id="event-drag" class="api-item">
<h3>drag( event, ui )<span class="returns">Type: <code>dialogdrag</code></span>
</h3>
<div>Triggered while the dialog is being dragged.</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
<ul>
<li>
<div><strong>position</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current CSS position of the dialog.</div>
</li>
<li>
<div><strong>offset</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current offset position of the dialog.</div>
</li>
</ul>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the dialog with the drag callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">drag: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the dialogdrag event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"dialogdrag"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div id="event-dragStart" class="api-item">
<h3>dragStart( event, ui )<span class="returns">Type: <code>dialogdragstart</code></span>
</h3>
<div>Triggered when the user starts dragging the dialog.</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
<ul>
<li>
<div><strong>position</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current CSS position of the dialog.</div>
</li>
<li>
<div><strong>offset</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current offset position of the dialog.</div>
</li>
</ul>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the dialog with the dragStart callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">dragStart: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the dialogdragstart event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"dialogdragstart"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div id="event-dragStop" class="api-item">
<h3>dragStop( event, ui )<span class="returns">Type: <code>dialogdragstop</code></span>
</h3>
<div>Triggered after the dialog has been dragged.</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
<ul>
<li>
<div><strong>position</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current CSS position of the dialog.</div>
</li>
<li>
<div><strong>offset</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current offset position of the dialog.</div>
</li>
</ul>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the dialog with the dragStop callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">dragStop: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the dialogdragstop event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"dialogdragstop"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div id="event-focus" class="api-item">
<h3>focus( event, ui )<span class="returns">Type: <code>dialogfocus</code></span>
</h3>
<div>Triggered when the dialog gains focus.</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the dialog with the focus callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">focus: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the dialogfocus event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"dialogfocus"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div id="event-open" class="api-item">
<h3>open( event, ui )<span class="returns">Type: <code>dialogopen</code></span>
</h3>
<div>Triggered when the dialog is opened.</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the dialog with the open callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">open: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the dialogopen event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"dialogopen"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div id="event-resize" class="api-item">
<h3>resize( event, ui )<span class="returns">Type: <code>dialogresize</code></span>
</h3>
<div>Triggered while the dialog is being resized.</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
<ul>
<li>
<div><strong>orginalPosition</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The CSS position of the dialog prior to being resized.</div>
</li>
<li>
<div><strong>position</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current CSS position of the dialog.</div>
</li>
<li>
<div><strong>originalSize</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The size of the dialog prior to being resized.</div>
</li>
<li>
<div><strong>size</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current size of the dialog.</div>
</li>
</ul>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the dialog with the resize callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">resize: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the dialogresize event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"dialogresize"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div id="event-resizeStart" class="api-item">
<h3>resizeStart( event, ui )<span class="returns">Type: <code>dialogresizestart</code></span>
</h3>
<div>Triggered when the user starts resizing the dialog.</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
<ul>
<li>
<div><strong>orginalPosition</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The CSS position of the dialog prior to being resized.</div>
</li>
<li>
<div><strong>position</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current CSS position of the dialog.</div>
</li>
<li>
<div><strong>originalSize</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The size of the dialog prior to being resized.</div>
</li>
<li>
<div><strong>size</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current size of the dialog.</div>
</li>
</ul>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the dialog with the resizeStart callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">resizeStart: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the dialogresizestart event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"dialogresizestart"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div id="event-resizeStop" class="api-item">
<h3>resizeStop( event, ui )<span class="returns">Type: <code>dialogresizestop</code></span>
</h3>
<div>Triggered after the dialog has been resized.</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
<ul>
<li>
<div><strong>orginalPosition</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The CSS position of the dialog prior to being resized.</div>
</li>
<li>
<div><strong>position</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current CSS position of the dialog.</div>
</li>
<li>
<div><strong>originalSize</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The size of the dialog prior to being resized.</div>
</li>
<li>
<div><strong>size</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current size of the dialog.</div>
</li>
</ul>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the dialog with the resizeStop callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).dialog({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">resizeStop: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the dialogresizestop event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"dialogresizestop"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div></section><section class="entry-examples" id="entry-examples"><header><h2 class="underline">Example:</h2></header><div class="entry-example" id="example-0">
<h4><span class="desc">A simple jQuery UI Dialog</span></h4>
<div class="syntaxhighlighter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="gutter"><div class="line number1 index0 alt2">1</div><div class="line number2 index1 alt1">2</div><div class="line number3 index2 alt2">3</div><div class="line number4 index3 alt1">4</div><div class="line number5 index4 alt2">5</div><div class="line number6 index5 alt1">6</div><div class="line number7 index6 alt2">7</div><div class="line number8 index7 alt1">8</div><div class="line number9 index8 alt2">9</div><div class="line number10 index9 alt1">10</div><div class="line number11 index10 alt2">11</div><div class="line number12 index11 alt1">12</div><div class="line number13 index12 alt2">13</div><div class="line number14 index13 alt1">14</div><div class="line number15 index14 alt2">15</div><div class="line number16 index15 alt1">16</div><div class="line number17 index16 alt2">17</div><div class="line number18 index17 alt1">18</div><div class="line number19 index18 alt2">19</div><div class="line number20 index19 alt1">20</div><div class="line number21 index20 alt2">21</div><div class="line number22 index21 alt1">22</div><div class="line number23 index22 alt2">23</div></td><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">&lt;!doctype html&gt;</code></div><div class="line number2 index1 alt1"><code class="plain">&lt;</code><code class="keyword">html</code> <code class="color1">lang</code><code class="plain">=</code><code class="string">"en"</code><code class="plain">&gt;</code></div><div class="line number3 index2 alt2"><code class="plain">&lt;</code><code class="keyword">head</code><code class="plain">&gt;</code></div><div class="line number4 index3 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">meta</code> <code class="color1">charset</code><code class="plain">=</code><code class="string">"utf-8"</code><code class="plain">&gt;</code></div><div class="line number5 index4 alt2"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">title</code><code class="plain">&gt;dialog demo&lt;/</code><code class="keyword">title</code><code class="plain">&gt;</code></div><div class="line number6 index5 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">link</code> <code class="color1">rel</code><code class="plain">=</code><code class="string">"stylesheet"</code> <code class="color1">href</code><code class="plain">=</code><code class="string">"<a href="http://code.jquery.com/ui/1.9.2/themes/base/jquery-ui.css">http://code.jquery.com/ui/1.9.2/themes/base/jquery-ui.css</a>"</code><code class="plain">&gt;</code></div><div class="line number7 index6 alt2"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">script</code> <code class="color1">src</code><code class="plain">=</code><code class="string">"<a href="http://code.jquery.com/jquery-1.8.3.js">http://code.jquery.com/jquery-1.8.3.js</a>"</code><code class="plain">&gt;&lt;/</code><code class="keyword">script</code><code class="plain">&gt;</code></div><div class="line number8 index7 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">script</code> <code class="color1">src</code><code class="plain">=</code><code class="string">"<a href="http://code.jquery.com/ui/1.9.2/jquery-ui.js">http://code.jquery.com/ui/1.9.2/jquery-ui.js</a>"</code><code class="plain">&gt;&lt;/</code><code class="keyword">script</code><code class="plain">&gt;</code></div><div class="line number9 index8 alt2"><code class="plain">&lt;/</code><code class="keyword">head</code><code class="plain">&gt;</code></div><div class="line number10 index9 alt1"><code class="plain">&lt;</code><code class="keyword">body</code><code class="plain">&gt;</code></div><div class="line number11 index10 alt2">&nbsp;</div><div class="line number12 index11 alt1"><code class="plain">&lt;</code><code class="keyword">button</code> <code class="color1">id</code><code class="plain">=</code><code class="string">"opener"</code><code class="plain">&gt;open the dialog&lt;/</code><code class="keyword">button</code><code class="plain">&gt;</code></div><div class="line number13 index12 alt2"><code class="plain">&lt;</code><code class="keyword">div</code> <code class="color1">id</code><code class="plain">=</code><code class="string">"dialog"</code> <code class="color1">title</code><code class="plain">=</code><code class="string">"Dialog Title"</code><code class="plain">&gt;I'm a dialog&lt;/</code><code class="keyword">div</code><code class="plain">&gt;</code></div><div class="line number14 index13 alt1">&nbsp;</div><div class="line number15 index14 alt2"><code class="plain">&lt;</code><code class="keyword">script</code><code class="plain">&gt;</code></div><div class="line number16 index15 alt1"><code class="plain">$( "#dialog" ).dialog({ autoOpen: false });</code></div><div class="line number17 index16 alt2"><code class="plain">$( "#opener" ).click(function() {</code></div><div class="line number18 index17 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">$( "#dialog" ).dialog( "open" );</code></div><div class="line number19 index18 alt2"><code class="plain">});</code></div><div class="line number20 index19 alt1"><code class="plain">&lt;/</code><code class="keyword">script</code><code class="plain">&gt;</code></div><div class="line number21 index20 alt2">&nbsp;</div><div class="line number22 index21 alt1"><code class="plain">&lt;/</code><code class="keyword">body</code><code class="plain">&gt;</code></div><div class="line number23 index22 alt2"><code class="plain">&lt;/</code><code class="keyword">html</code><code class="plain">&gt;</code></div></div></td></tr></tbody></table></div>
<h4>Demo:</h4>
<div class="demo code-demo"></div>
</div></section>
</div></article>

</body>
</html>
