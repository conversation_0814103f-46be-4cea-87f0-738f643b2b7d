<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>jQuery UI Button - Radios</title>
	<link rel="stylesheet" href="../../themes/base/jquery.ui.all.css">
	<script src="../../jquery-1.9.1.js"></script>
	<script src="../../ui/jquery.ui.core.js"></script>
	<script src="../../ui/jquery.ui.widget.js"></script>
	<script src="../../ui/jquery.ui.button.js"></script>
	<link rel="stylesheet" href="../demos.css">
	<script>
	$(function() {
		$( "#radio" ).buttonset();
	});
	</script>
</head>
<body>

<form>
	<div id="radio">
		<input type="radio" id="radio1" name="radio" /><label for="radio1">Choice 1</label>
		<input type="radio" id="radio2" name="radio" checked="checked" /><label for="radio2">Choice 2</label>
		<input type="radio" id="radio3" name="radio" /><label for="radio3">Choice 3</label>
	</div>
</form>

<div class="demo-description">
<p>A set of three radio buttons transformed into a button set.</p>
</div>
</body>
</html>
