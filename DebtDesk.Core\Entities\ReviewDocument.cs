using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DebtDesk.Core.Entities
{
    /// <summary>
    /// Represents documents related to reviews
    /// </summary>
    public class ReviewDocument : BaseEntity
    {
        [Required]
        [MaxLength(200)]
        public string FileName { get; set; } = string.Empty;

        public byte? Type { get; set; }

        public DateTime UpTime { get; set; }

        public DateTime? Send { get; set; }

        // Foreign key
        public int ReviewId { get; set; }

        // Navigation properties
        [ForeignKey("ReviewId")]
        public virtual Review Review { get; set; } = null!;
    }
}
