using DebtDesk.Core.Entities;

namespace DebtDesk.Core.Interfaces
{
    public interface IPaymentScheduleRepository : IRepository<PaymentSchedule>
    {
        Task<DateTime?> GetLastPaymentScheduleDateAsync(string dluEvCislo);
        Task<PaymentSchedule?> GetLastSALPaymentScheduleAsync(string dluEvCislo, int autoGenFileId);
        Task<int> GetPaymentScheduleCountAsync(string dluEvCislo);
        Task<IEnumerable<PaymentSchedule>> GetPaymentSchedulesByDebtCaseAsync(string dluEvCislo);
        Task<int> GetPaidPaymentSchedulesCountAsync(string dluEvCislo);
    }
}
