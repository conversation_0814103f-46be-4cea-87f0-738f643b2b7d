using System.ComponentModel.DataAnnotations;

namespace DebtDesk.Core.Entities
{
    public class File : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [MaxLength(50)]
        public string Md5 { get; set; } = string.Empty;

        public int Time { get; set; }

        public int Size { get; set; }

        [Required]
        [MaxLength(80)]
        public string Mime { get; set; } = string.Empty;

        public DateTime XmlDate { get; set; }

        [MaxLength(50)]
        public string? Dir { get; set; }

        public byte Status { get; set; }

        [MaxLength(100)]
        public string? SendFileName { get; set; }

        public DateTime? SendFileTime { get; set; }

        // Navigation properties
        public virtual ICollection<DebtCase> DebtCases { get; set; } = new List<DebtCase>();
        public virtual ICollection<PaymentSchedule> PaymentSchedules { get; set; } = new List<PaymentSchedule>();
        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();
        public virtual ICollection<Review> Reviews { get; set; } = new List<Review>();
    }
}
