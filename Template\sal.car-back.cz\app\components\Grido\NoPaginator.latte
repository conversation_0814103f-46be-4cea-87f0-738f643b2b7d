{**
 * This file is part of the Grido (http://grido.bugyik.cz)
 *
 * Copyright (c) 2011 <PERSON><PERSON> (http://petr.bugyik.cz)
 *
 * For the full copyright and license information, please view
 * the file LICENSE.md that was distributed with this source code.
 *}

{snippet grid}
{?
    $form = $control->getComponent('form');
    $form->getElementPrototype()->class[] = 'ajax grido';

    $operation = $control->hasOperation();
    $actions = $control->hasActions() ? $control->getComponent(\Grido\Components\Actions\Action::ID)->getComponents() : array();

    $filters = $control->hasFilters() ? $form->getComponent(\Grido\Components\Filters\Filter::ID)->getComponents() : array();
    $filterRenderType = $control->getFilterRenderType();

    $columns = $control->getComponent(Grido\Components\Columns\Column::ID)->getComponents();
    $columnCount = count($columns) + ($operation ? 1 : 0);
    $showActionsColumn = $actions || ($filters && $filterRenderType == Grido\Components\Filters\Filter::RENDER_INNER);

    /* TWITTER BOOTSTRAP 2/3 */
    $buttons = $form->getComponent('buttons');
    $buttons->getComponent('search')->getControlPrototype()->class[] = 'btn btn-default btn-sm search';
    $buttons->getComponent('reset')->getControlPrototype()->class[] = 'btn btn-default btn-sm reset';

    $form['count']->controlPrototype->class[] = 'form-control';
    $operation ? $form['operations']['operations']->controlPrototype->class[] = 'form-control' : NULL}

    {foreach $filters as $filter}
        {?$filter->controlPrototype->class[] = 'form-control'}
    {/foreach}

    {foreach $actions as $action}
        {?
            $element = $action->getElementPrototype();
            $element->class[] = 'btn btn-default btn-xs btn-mini';
        }
        {if $icon = $action->getOption('icon')}
        {?
            $element->setText(' ' . $action->getLabel());
            $element->insert(0, \Nette\Utils\Html::el('i')->setClass(array("glyphicon glyphicon-$icon icon-$icon")));
        }
        {/if}
    {/foreach}
{if $form->getErrors()}
<ul n:foreach="$form->getErrors() as $error">
    <li>{$error}</li>
</ul>
{/if}
{form form}
{if $filterRenderType == Grido\Components\Filters\Filter::RENDER_OUTER}
    <div n:block="outerFilter" class="filter outer">
        <div class="items">
            <span n:foreach="$filters as $filter" class="grid-filter-{$filter->getName()}">
                {$filter->getLabel()}
                {$filter->getControl()}
            </span>
        </div>
        <div class="buttons">
            {formContainer buttons}
                {if $filters}
                    {input search}
                {/if}
{*                
                {input reset}
*}
            {/formContainer}
        </div>
    </div>
{/if}

{block table}
{!$control->getTablePrototype()->startTag()}
    <thead>
        <tr class="head">
            <th n:if="$operation" class="checker"{if $filters} rowspan="{if $filterRenderType == Grido\Components\Filters\Filter::RENDER_OUTER}1{else}2{/if}"{/if}>
                <input type="checkbox" title="{_'Invert'}">
            </th>
            {foreach $columns as $column}
                {!$column->getHeaderPrototype()->startTag()}
                    {if $column->isSortable()}
                        <a n:if="!$column->getSort()" n:href="sort! [$column->getName() => Grido\Components\Columns\Column::ORDER_ASC]" class="ajax">{_$column->getLabel()}</a>
                        <a n:if="$column->getSort() == Grido\Components\Columns\Column::ORDER_ASC" n:href="sort! [$column->getName() => Grido\Components\Columns\Column::ORDER_DESC]" class="sort ajax">{_$column->getLabel()}</a>
                        <a n:if="$column->getSort() == Grido\Components\Columns\Column::ORDER_DESC" n:href="sort! [$column->getName() => Grido\Components\Columns\Column::ORDER_ASC]" class="sort ajax">{_$column->getLabel()}</a>
                        <span></span>
                    {else}
                        {_$column->getLabel()}
                    {/if}
                {!$column->getHeaderPrototype()->endTag()}
            {/foreach}
            <th n:if="$showActionsColumn" class="actions center">
                {_'Actions'}
            </th>
        </tr>
        <tr n:if="$filterRenderType == Grido\Components\Filters\Filter::RENDER_INNER && $filters" class="filter inner">
            {foreach $columns as $column}
                {if $column->hasFilter()}
                    {!$control->getFilter($column->getName())->getWrapperPrototype()->startTag()}
                    {formContainer filters}
                        {input $column->getName()}
                    {/formContainer}
                    {!$control->getFilter($column->getName())->getWrapperPrototype()->endTag()}
                {elseif $column->headerPrototype->rowspan != 2}
                    <th>&nbsp;</th>
                {/if}
            {/foreach}

            <th n:if="$filters" class="buttons">
{*
                {formContainer buttons}
                    {input search}
                    {input reset}
                {/formContainer}
*}
            </th>
        </tr>
    </thead>
    <tfoot>
{*
        <tr>
            <td colspan="{=$showActionsColumn ? $columnCount + 1 : $columnCount}">
                <span n:if="$operation" n:block="operations" class="operations"  title="{_'Select some row'}">
                    {$form[Grido\Components\Operation::ID][Grido\Components\Operation::ID]->control}
                    {?$form[Grido\Grid::BUTTONS][Grido\Components\Operation::ID]->controlPrototype->class[] = 'hide'}
                    {$form[Grido\Grid::BUTTONS][Grido\Components\Operation::ID]->control}
                </span>
                <span n:if="$paginator->steps && $paginator->pageCount > 1" n:block="paginator" class="paginator">
                    {if $control->page == 1}
                        <span class="btn btn-default btn-xs btn-mini disabled" n:href="page! page => $paginator->getPage() - 1"><i class="glyphicon glyphicon-arrow-left icon-arrow-left"></i> {_'Previous'}</span>
                    {else}
                        <a class="btn btn-default btn-xs btn-mini ajax" n:href="page! page => $paginator->getPage() - 1"><i class="glyphicon glyphicon-arrow-left icon-arrow-left"></i> {_'Previous'}</a>
                    {/if}
                    {var $steps = $paginator->getSteps()}
                    {foreach $steps as $step}
                        {if $step == $control->page}
                            <span class="btn btn-default btn-xs btn-mini disabled">{$step}</span>
                        {else}
                            <a class="btn btn-default btn-xs btn-mini ajax" n:href="page! page => $step">{$step}</a>
                        {/if}
                        <a n:if="$iterator->nextValue > $step + 1" class="prompt" data-grido-prompt="{_'Enter page:'}" data-grido-link="{link page! page => 0}">...</a>
                        {var $prevStep = $step}
                    {/foreach}
                    {if $control->page == $paginator->getPageCount()}
                        <span class="btn btn-default btn-xs btn-mini disabled" n:href="page! page => $paginator->getPage() + 1">{_'Next'} <i class="glyphicon glyphicon-arrow-right icon-arrow-right"></i></span>
                    {else}
                        <a class="btn btn-default btn-xs btn-mini ajax" n:href="page! page => $paginator->getPage() + 1">{_'Next'} <i class="glyphicon glyphicon-arrow-right icon-arrow-right"></i></a>
                    {/if}
                </span>
                <span n:block="count" class="count">
                    {= sprintf($template->translate('Items %d - %d of %d'), $paginator->getCountBegin(), $paginator->getCountEnd(), $control->getCount())}
                    {input count}
                    {formContainer buttons}
                        {input perPage, class => 'hide'}
                    {/formContainer}
                    <a n:if="$control->hasExport()" class="btn btn-default btn-xs btn-mini" href="{=$control->getComponent(Grido\Components\Export::ID)->link('export!')}" title="{_'Export all items'}"><i class="glyphicon glyphicon-download icon-download"></i></a>
                </span>
            </td>
        </tr>
             *}
    </tfoot>
    <tbody>
        {var $propertyAccessor = $control->getPropertyAccessor()}
        {foreach $data as $row}
            {? $checkbox = $operation
                    ? $form[Grido\Components\Operation::ID][$propertyAccessor->getProperty($row, $control->getComponent(Grido\Components\Operation::ID)->getPrimaryKey())]
                    : NULL;
                $tr = $control->getRowPrototype($row);
                $tr->class[] = $checkbox && $checkbox->getValue()
                    ? 'selected'
                    : NULL;
            }
            {!$tr->startTag()}
                <td n:if="$checkbox" class="checker">
                    {$checkbox->getControl()}
                </td>
                {foreach $columns as $column}
                    {?$td = $column->getCellPrototype($row)}
                    {!$td->startTag()}
                        {if is_string($column->getCustomRender())}
                            {include $column->getCustomRender() control => $control, presenter => $control->getPresenter(), item => $row}
                        {else}
                            {!$column->render($row)}
                        {/if}
                    {!$td->endTag()}
                {/foreach}
                <td n:if="$showActionsColumn" class="actions center">
                    {foreach $actions as $action}
                        {control $action $row}
                    {/foreach}
                    {if !$actions}
                        &nbsp;
                    {/if}
                </td>
            {!$tr->endTag()}
        {/foreach}
        <tr n:if="!$control->getCount()"><td colspan="{=$showActionsColumn ? $columnCount + 1 : $columnCount}" class="no-results">{_'No results.'}</td></tr>
    </tbody>
{!$control->getTablePrototype()->endTag()}
{/block}
{/form}
{/snippet}
