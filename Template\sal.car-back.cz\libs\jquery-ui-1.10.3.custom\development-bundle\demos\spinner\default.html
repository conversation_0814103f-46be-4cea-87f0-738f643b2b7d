<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>jQuery UI Spinner - Default functionality</title>
	<link rel="stylesheet" href="../../themes/base/jquery.ui.all.css">
	<script src="../../jquery-1.9.1.js"></script>
	<script src="../../external/jquery.mousewheel.js"></script>
	<script src="../../ui/jquery.ui.core.js"></script>
	<script src="../../ui/jquery.ui.widget.js"></script>
	<script src="../../ui/jquery.ui.button.js"></script>
	<script src="../../ui/jquery.ui.spinner.js"></script>
	<link rel="stylesheet" href="../demos.css">
	<script>
	$(function() {
		var spinner = $( "#spinner" ).spinner();

		$( "#disable" ).click(function() {
			if ( spinner.spinner( "option", "disabled" ) ) {
				spinner.spinner( "enable" );
			} else {
				spinner.spinner( "disable" );
			}
		});
		$( "#destroy" ).click(function() {
			if ( spinner.data( "ui-spinner" ) ) {
				spinner.spinner( "destroy" );
			} else {
				spinner.spinner();
			}
		});
		$( "#getvalue" ).click(function() {
			alert( spinner.spinner( "value" ) );
		});
		$( "#setvalue" ).click(function() {
			spinner.spinner( "value", 5 );
		});

		$( "button" ).button();
	});
	</script>
</head>
<body>

<p>
	<label for="spinner">Select a value:</label>
	<input id="spinner" name="value">
</p>

<p>
	<button id="disable">Toggle disable/enable</button>
	<button id="destroy">Toggle widget</button>
</p>

<p>
	<button id="getvalue">Get value</button>
	<button id="setvalue">Set value to 5</button>
</p>

<div class="demo-description">
<p>Default spinner.</p>
</div>
</body>
</html>
