using Microsoft.EntityFrameworkCore;
using DebtDesk.Core.Entities;
using DebtDesk.Core.Interfaces;
using DebtDesk.Infrastructure.Data;

namespace DebtDesk.Infrastructure.Repositories
{
    public class PaymentScheduleRepository : Repository<PaymentSchedule>, IPaymentScheduleRepository
    {
        public PaymentScheduleRepository(DebtDeskDbContext context) : base(context)
        {
        }

        public async Task<DateTime?> GetLastPaymentScheduleDateAsync(string dluEvCislo)
        {
            var lastSchedule = await _dbSet
                .Where(ps => ps.DluEvCislo == dluEvCislo && ps.TypSplatky == "SP")
                .OrderByDescending(ps => ps.DatumSplatnostiSplatky)
                .FirstOrDefaultAsync();

            return lastSchedule?.DatumSplatnostiSplatky;
        }

        public async Task<PaymentSchedule?> GetLastSALPaymentScheduleAsync(string dluEvCislo, int autoGenFileId)
        {
            return await _dbSet
                .Where(ps => ps.DluEvCislo == dluEvCislo && 
                           ps.TypSplatky == "SP" && 
                           ps.FileId != autoGenFileId)
                .OrderByDescending(ps => ps.DatumSplatnostiSplatky)
                .FirstOrDefaultAsync();
        }

        public async Task<int> GetPaymentScheduleCountAsync(string dluEvCislo)
        {
            return await _dbSet
                .Where(ps => ps.DluEvCislo == dluEvCislo && ps.TypSplatky == "SP")
                .CountAsync();
        }

        public async Task<IEnumerable<PaymentSchedule>> GetPaymentSchedulesByDebtCaseAsync(string dluEvCislo)
        {
            return await _dbSet
                .Include(ps => ps.File)
                .Where(ps => ps.DluEvCislo == dluEvCislo)
                .OrderBy(ps => ps.DatumSplatnostiSplatky)
                .ToListAsync();
        }

        public async Task<int> GetPaidPaymentSchedulesCountAsync(string dluEvCislo)
        {
            return await _dbSet
                .Where(ps => ps.DluEvCislo == dluEvCislo && 
                           ps.TypSplatky == "SP" && 
                           ps.VyseZustatku == "0")
                .CountAsync();
        }
    }
}
