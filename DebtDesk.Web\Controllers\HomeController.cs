using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using DebtDesk.Web.Models;
using DebtDesk.Core.Enums;

namespace DebtDesk.Web.Controllers;

[Authorize]
public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;

    public HomeController(ILogger<HomeController> logger)
    {
        _logger = logger;
    }

    public IActionResult Index()
    {
        var userRole = User.FindFirst(ClaimTypes.Role)?.Value;

        // Redirect based on user role (similar to PHP logic)
        return userRole switch
        {
            UserRole.REVIEW when !User.IsInRole(UserRole.DLU) => RedirectToAction("Index", "Reviews"),
            UserRole.PISPRIPADY when !User.IsInRole(UserRole.DLU) => RedirectToAction("Index", "WrittenCases"),
            UserRole.TELPRIPADY when !User.IsInRole(UserRole.DLU) => RedirectToAction("Index", "PhoneCases"),
            _ => RedirectToAction("Index", "DebtCases")
        };
    }

    public IActionResult Privacy()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}
