<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>jQuery UI progressbar documentation</title>

	<style>
	body {
		font-family: "Trebuchet MS", "Arial", "Helvetica", "Verdana", "sans-serif"
	}
	.gutter {
		display: none;
	}
	</style>
</head>
<body>

<script>{
		"title":
			"Progressbar Widget",
		"excerpt":
			"Display status of a determinate or indeterminate process.",
		"termSlugs": {
			"category": [
				"widgets"
			]
		}
	}</script><article id="progressbar1" class="entry widget"><h2 class="section-title">
<span>Progressbar Widget</span><span class="version-details">version added: 1.6</span>
</h2>
<div class="entry-wrapper">
<p class="desc"><strong>Description: </strong>Display status of a determinate or indeterminate process.</p>
<section id="quick-nav"><header><h2>QuickNav<a href="#entry-examples">Examples</a>
</h2></header><div class="quick-nav-section">
<h3>Options</h3>
<div><a href="#option-disabled">disabled</a></div>
<div><a href="#option-max">max</a></div>
<div><a href="#option-value">value</a></div>
</div>
<div class="quick-nav-section">
<h3>Methods</h3>
<div><a href="#method-destroy">destroy</a></div>
<div><a href="#method-disable">disable</a></div>
<div><a href="#method-enable">enable</a></div>
<div><a href="#method-option">option</a></div>
<div><a href="#method-value">value</a></div>
<div><a href="#method-widget">widget</a></div>
</div>
<div class="quick-nav-section">
<h3>Events</h3>
<div><a href="#event-change">change</a></div>
<div><a href="#event-create">create</a></div>
<div><a href="#event-complete">complete</a></div>
</div></section><div class="longdesc" id="entry-longdesc">
		<p>
			The progress bar is designed to display the current percent complete for a process. The bar is coded to be flexibly sized through CSS and will scale to fit inside its parent container by default.
		</p>
		<p>
			A determinate progress bar should only be used in situations where the system can accurately update the current status. A determinate progress bar should never fill from left to right, then loop back to empty for a single process — if the actual status cannot be calculated, an indeterminate progress bar should be used to provide user feedback.
		</p>
	</div>
<h3>Additional Notes:</h3>
<div class="longdesc"><ul><li>
			This widget requires some functional CSS, otherwise it won't work. If you build a custom theme, use the widget's specific CSS file as a starting point.
		</li></ul></div>
<section id="options"><header><h2 class="underline">Options</h2></header><div id="option-disabled" class="api-item first-item">
<h3>disabled<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Disables the progressbar if set to <code>true</code>.</div>
<strong>Code examples:</strong><p>Initialize the progressbar with the disabled option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).progressbar({ disabled: </code><code class="keyword">true</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the disabled option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">disabled = $( </code><code class="string">".selector"</code> <code class="plain">).progressbar( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"disabled"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).progressbar( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"disabled"</code><code class="plain">, </code><code class="keyword">true</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-max" class="api-item">
<h3>max<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Number">Number</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>100</code>
</div>
<div>The maximum value of the progressbar.</div>
<strong>Code examples:</strong><p>Initialize the progressbar with the max option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).progressbar({ max: 1024 });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the max option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">max = $( </code><code class="string">".selector"</code> <code class="plain">).progressbar( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"max"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).progressbar( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"max"</code><code class="plain">, 1024 );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-value" class="api-item">
<h3>value<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Number">Number</a> or <a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>0</code>
</div>
<div>The value of the progressbar.</div>
<strong>Multiple types supported:</strong><ul>
<li>
<strong>Number</strong>: 
					A value between <code>0</code> and the <a href="#option-max"><code>max</code></a>.
				</li>
<li>
<strong>Boolean</strong>: 
					Value can be set to <code>false</code> to create an indeterminate progressbar.
				</li>
</ul>
<strong>Code examples:</strong><p>Initialize the progressbar with the value option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).progressbar({ value: 25 });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the value option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">value = $( </code><code class="string">".selector"</code> <code class="plain">).progressbar( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"value"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).progressbar( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"value"</code><code class="plain">, 25 );</code></div></div></td></tr></tbody></table></div>
</div></section><section id="methods"><header><h2 class="underline">Methods</h2></header><div id="method-destroy"><div class="api-item first-item">
<h3>destroy()</h3>
<div>
		Removes the progressbar functionality completely. This will return the element back to its pre-init state.
	</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the destroy method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).progressbar( </code><code class="string">"destroy"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-disable"><div class="api-item">
<h3>disable()</h3>
<div>
		Disables the progressbar.
	</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the disable method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).progressbar( </code><code class="string">"disable"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-enable"><div class="api-item">
<h3>enable()</h3>
<div>
		Enables the progressbar.
	</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the enable method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).progressbar( </code><code class="string">"enable"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-option">
<div class="api-item">
<h3>option( optionName )<span class="returns">Returns: <a href="http://api.jquery.com/Types#Object">Object</a></span>
</h3>
<div>Gets the value currently associated with the specified <code>optionName</code>.</div>
<ul><li>
<div><strong>optionName</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#String">String</a>
</div>
<div>The name of the option to get.</div>
</li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the  method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="keyword">var</code> <code class="plain">isDisabled = $( </code><code class="string">".selector"</code> <code class="plain">).progressbar( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"disabled"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div class="api-item">
<h3>option()<span class="returns">Returns: <a href="http://api.jquery.com/Types#PlainObject">PlainObject</a></span>
</h3>
<div>Gets an object containing key/value pairs representing the current progressbar options hash.</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the  method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="keyword">var</code> <code class="plain">options = $( </code><code class="string">".selector"</code> <code class="plain">).progressbar( </code><code class="string">"option"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div class="api-item">
<h3>option( optionName, value )</h3>
<div>Sets the value of the progressbar option associated with the specified <code>optionName</code>.</div>
<ul>
<li>
<div><strong>optionName</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#String">String</a>
</div>
<div>The name of the option to set.</div>
</li>
<li>
<div><strong>value</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>A value to set for the option.</div>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Invoke the  method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).progressbar( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"disabled"</code><code class="plain">, </code><code class="keyword">true</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div class="api-item">
<h3>option( options )</h3>
<div>Sets one or more options for the progressbar.</div>
<ul><li>
<div><strong>options</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>A map of option-value pairs to set.</div>
</li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the  method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).progressbar( </code><code class="string">"option"</code><code class="plain">, { disabled: </code><code class="keyword">true</code> <code class="plain">} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
</div>
<div id="method-value">
<div class="api-item">
<h3>value()<span class="returns">Returns: <a href="http://api.jquery.com/Types#Number">Number</a> or <a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div>Gets the current value of the progressbar.</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the  method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="keyword">var</code> <code class="plain">progressSoFar = $( </code><code class="string">".selector"</code> <code class="plain">).progressbar( </code><code class="string">"value"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div class="api-item">
<h3>value( value )</h3>
<div>Sets the current value of the progressbar.</div>
<ul><li>
<div><strong>value</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Number">Number</a> or <a href="http://api.jquery.com/Types#Boolean">Boolean</a>
</div>
<div>The value to set. See the <a href="#option-value"><code>value</code></a> option for details on valid values.</div>
</li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the  method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).progressbar( </code><code class="string">"value"</code><code class="plain">, 50 );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
</div>
<div id="method-widget"><div class="api-item">
<h3>widget()<span class="returns">Returns: <a href="http://api.jquery.com/Types#jQuery">jQuery</a></span>
</h3>
<div>
		Returns a <code>jQuery</code> object containing the progressbar.
	</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the widget method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="keyword">var</code> <code class="plain">widget = $( </code><code class="string">".selector"</code> <code class="plain">).progressbar( </code><code class="string">"widget"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div></section><section id="events"><header><h2 class="underline">Events</h2></header><div id="event-change" class="api-item first-item">
<h3>change( event, ui )<span class="returns">Type: <code>progressbarchange</code></span>
</h3>
<div>Triggered when the value of the progressbar changes.</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the progressbar with the change callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).progressbar({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">change: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the progressbarchange event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"progressbarchange"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div id="event-complete" class="api-item">
<h3>complete( event, ui )<span class="returns">Type: <code>progressbarcomplete</code></span>
</h3>
<div>Triggered when the value of the progressbar reaches the maximum value.</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the progressbar with the complete callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).progressbar({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">complete: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the progressbarcomplete event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"progressbarcomplete"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div id="event-create" class="api-item">
<h3>create( event, ui )<span class="returns">Type: <code>progressbarcreate</code></span>
</h3>
<div>
		Triggered when the progressbar is created.
	</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the progressbar with the create callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).progressbar({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">create: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the progressbarcreate event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"progressbarcreate"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div></section><section class="entry-examples" id="entry-examples"><header><h2 class="underline">Examples:</h2></header><div class="entry-example" id="example-0">
<h4>Example: <span class="desc">A simple jQuery UI Progressbar</span>
</h4>
<div class="syntaxhighlighter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="gutter"><div class="line number1 index0 alt2">1</div><div class="line number2 index1 alt1">2</div><div class="line number3 index2 alt2">3</div><div class="line number4 index3 alt1">4</div><div class="line number5 index4 alt2">5</div><div class="line number6 index5 alt1">6</div><div class="line number7 index6 alt2">7</div><div class="line number8 index7 alt1">8</div><div class="line number9 index8 alt2">9</div><div class="line number10 index9 alt1">10</div><div class="line number11 index10 alt2">11</div><div class="line number12 index11 alt1">12</div><div class="line number13 index12 alt2">13</div><div class="line number14 index13 alt1">14</div><div class="line number15 index14 alt2">15</div><div class="line number16 index15 alt1">16</div><div class="line number17 index16 alt2">17</div><div class="line number18 index17 alt1">18</div><div class="line number19 index18 alt2">19</div><div class="line number20 index19 alt1">20</div><div class="line number21 index20 alt2">21</div></td><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">&lt;!doctype html&gt;</code></div><div class="line number2 index1 alt1"><code class="plain">&lt;</code><code class="keyword">html</code> <code class="color1">lang</code><code class="plain">=</code><code class="string">"en"</code><code class="plain">&gt;</code></div><div class="line number3 index2 alt2"><code class="plain">&lt;</code><code class="keyword">head</code><code class="plain">&gt;</code></div><div class="line number4 index3 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">meta</code> <code class="color1">charset</code><code class="plain">=</code><code class="string">"utf-8"</code><code class="plain">&gt;</code></div><div class="line number5 index4 alt2"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">title</code><code class="plain">&gt;progressbar demo&lt;/</code><code class="keyword">title</code><code class="plain">&gt;</code></div><div class="line number6 index5 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">link</code> <code class="color1">rel</code><code class="plain">=</code><code class="string">"stylesheet"</code> <code class="color1">href</code><code class="plain">=</code><code class="string">"<a href="http://code.jquery.com/ui/1.9.2/themes/base/jquery-ui.css">http://code.jquery.com/ui/1.9.2/themes/base/jquery-ui.css</a>"</code><code class="plain">&gt;</code></div><div class="line number7 index6 alt2"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">script</code> <code class="color1">src</code><code class="plain">=</code><code class="string">"<a href="http://code.jquery.com/jquery-1.8.3.js">http://code.jquery.com/jquery-1.8.3.js</a>"</code><code class="plain">&gt;&lt;/</code><code class="keyword">script</code><code class="plain">&gt;</code></div><div class="line number8 index7 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">script</code> <code class="color1">src</code><code class="plain">=</code><code class="string">"<a href="http://code.jquery.com/ui/1.9.2/jquery-ui.js">http://code.jquery.com/ui/1.9.2/jquery-ui.js</a>"</code><code class="plain">&gt;&lt;/</code><code class="keyword">script</code><code class="plain">&gt;</code></div><div class="line number9 index8 alt2"><code class="plain">&lt;/</code><code class="keyword">head</code><code class="plain">&gt;</code></div><div class="line number10 index9 alt1"><code class="plain">&lt;</code><code class="keyword">body</code><code class="plain">&gt;</code></div><div class="line number11 index10 alt2">&nbsp;</div><div class="line number12 index11 alt1"><code class="plain">&lt;</code><code class="keyword">div</code> <code class="color1">id</code><code class="plain">=</code><code class="string">"progressbar"</code><code class="plain">&gt;&lt;/</code><code class="keyword">div</code><code class="plain">&gt;</code></div><div class="line number13 index12 alt2">&nbsp;</div><div class="line number14 index13 alt1"><code class="plain">&lt;</code><code class="keyword">script</code><code class="plain">&gt;</code></div><div class="line number15 index14 alt2"><code class="plain">$( "#progressbar" ).progressbar({</code></div><div class="line number16 index15 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">value: 37</code></div><div class="line number17 index16 alt2"><code class="plain">});</code></div><div class="line number18 index17 alt1"><code class="plain">&lt;/</code><code class="keyword">script</code><code class="plain">&gt;</code></div><div class="line number19 index18 alt2">&nbsp;</div><div class="line number20 index19 alt1"><code class="plain">&lt;/</code><code class="keyword">body</code><code class="plain">&gt;</code></div><div class="line number21 index20 alt2"><code class="plain">&lt;/</code><code class="keyword">html</code><code class="plain">&gt;</code></div></div></td></tr></tbody></table></div>
<h4>Demo:</h4>
<div class="demo code-demo" data-height="50"></div>
</div>
<div class="entry-example" id="example-1">
<h4>Example: <span class="desc">A simple jQuery UI Indeterminate Progressbar</span>
</h4>
<div class="syntaxhighlighter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="gutter"><div class="line number1 index0 alt2">1</div><div class="line number2 index1 alt1">2</div><div class="line number3 index2 alt2">3</div><div class="line number4 index3 alt1">4</div><div class="line number5 index4 alt2">5</div><div class="line number6 index5 alt1">6</div><div class="line number7 index6 alt2">7</div><div class="line number8 index7 alt1">8</div><div class="line number9 index8 alt2">9</div><div class="line number10 index9 alt1">10</div><div class="line number11 index10 alt2">11</div><div class="line number12 index11 alt1">12</div><div class="line number13 index12 alt2">13</div><div class="line number14 index13 alt1">14</div><div class="line number15 index14 alt2">15</div><div class="line number16 index15 alt1">16</div><div class="line number17 index16 alt2">17</div><div class="line number18 index17 alt1">18</div><div class="line number19 index18 alt2">19</div><div class="line number20 index19 alt1">20</div><div class="line number21 index20 alt2">21</div></td><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">&lt;!doctype html&gt;</code></div><div class="line number2 index1 alt1"><code class="plain">&lt;</code><code class="keyword">html</code> <code class="color1">lang</code><code class="plain">=</code><code class="string">"en"</code><code class="plain">&gt;</code></div><div class="line number3 index2 alt2"><code class="plain">&lt;</code><code class="keyword">head</code><code class="plain">&gt;</code></div><div class="line number4 index3 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">meta</code> <code class="color1">charset</code><code class="plain">=</code><code class="string">"utf-8"</code><code class="plain">&gt;</code></div><div class="line number5 index4 alt2"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">title</code><code class="plain">&gt;progressbar demo&lt;/</code><code class="keyword">title</code><code class="plain">&gt;</code></div><div class="line number6 index5 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">link</code> <code class="color1">rel</code><code class="plain">=</code><code class="string">"stylesheet"</code> <code class="color1">href</code><code class="plain">=</code><code class="string">"<a href="http://code.jquery.com/ui/1.9.2/themes/base/jquery-ui.css">http://code.jquery.com/ui/1.9.2/themes/base/jquery-ui.css</a>"</code><code class="plain">&gt;</code></div><div class="line number7 index6 alt2"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">script</code> <code class="color1">src</code><code class="plain">=</code><code class="string">"<a href="http://code.jquery.com/jquery-1.8.3.js">http://code.jquery.com/jquery-1.8.3.js</a>"</code><code class="plain">&gt;&lt;/</code><code class="keyword">script</code><code class="plain">&gt;</code></div><div class="line number8 index7 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">script</code> <code class="color1">src</code><code class="plain">=</code><code class="string">"<a href="http://code.jquery.com/ui/1.9.2/jquery-ui.js">http://code.jquery.com/ui/1.9.2/jquery-ui.js</a>"</code><code class="plain">&gt;&lt;/</code><code class="keyword">script</code><code class="plain">&gt;</code></div><div class="line number9 index8 alt2"><code class="plain">&lt;/</code><code class="keyword">head</code><code class="plain">&gt;</code></div><div class="line number10 index9 alt1"><code class="plain">&lt;</code><code class="keyword">body</code><code class="plain">&gt;</code></div><div class="line number11 index10 alt2">&nbsp;</div><div class="line number12 index11 alt1"><code class="plain">&lt;</code><code class="keyword">div</code> <code class="color1">id</code><code class="plain">=</code><code class="string">"progressbar"</code><code class="plain">&gt;&lt;/</code><code class="keyword">div</code><code class="plain">&gt;</code></div><div class="line number13 index12 alt2">&nbsp;</div><div class="line number14 index13 alt1"><code class="plain">&lt;</code><code class="keyword">script</code><code class="plain">&gt;</code></div><div class="line number15 index14 alt2"><code class="plain">$( "#progressbar" ).progressbar({</code></div><div class="line number16 index15 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">value: false</code></div><div class="line number17 index16 alt2"><code class="plain">});</code></div><div class="line number18 index17 alt1"><code class="plain">&lt;/</code><code class="keyword">script</code><code class="plain">&gt;</code></div><div class="line number19 index18 alt2">&nbsp;</div><div class="line number20 index19 alt1"><code class="plain">&lt;/</code><code class="keyword">body</code><code class="plain">&gt;</code></div><div class="line number21 index20 alt2"><code class="plain">&lt;/</code><code class="keyword">html</code><code class="plain">&gt;</code></div></div></td></tr></tbody></table></div>
<h4>Demo:</h4>
<div class="demo code-demo" data-height="50"></div>
</div></section>
</div></article>

</body>
</html>
