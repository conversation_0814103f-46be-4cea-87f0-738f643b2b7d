<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>jQuery UI mouse documentation</title>

	<style>
	body {
		font-family: "Trebuchet MS", "Arial", "Helvetica", "Verdana", "sans-serif"
	}
	.gutter {
		display: none;
	}
	</style>
</head>
<body>

<script>{
		"title":
			"Mouse Interaction",
		"excerpt":
			"The base interaction layer.",
		"termSlugs": {
			"category": [
				"utilities","interactions"
			]
		}
	}</script><article id="jQuery-ui-mouse1" class="entry widget"><h2 class="section-title"><span>Mouse Interaction</span></h2>
<div class="entry-wrapper">
<p class="desc"><strong>Description: </strong>The base interaction layer.</p>
<section id="quick-nav"><header><h2>QuickNav</h2></header><div class="quick-nav-section">
<h3>Options</h3>
<div><a href="#option-cancel">cancel</a></div>
<div><a href="#option-delay">delay</a></div>
<div><a href="#option-distance">distance</a></div>
</div>
<div class="quick-nav-section">
<h3>Methods</h3>
<div><a href="#method-_mouseInit">_mouseInit</a></div>
<div><a href="#method-_mouseDestroy">_mouseDestroy</a></div>
<div><a href="#method-_mouseDown">_mouseDown</a></div>
<div><a href="#method-_mouseMove">_mouseMove</a></div>
<div><a href="#method-_mouseUp">_mouseUp</a></div>
<div><a href="#method-_mouseDistanceMet">_mouseDistanceMet</a></div>
<div><a href="#method-_mouseDelayMet">_mouseDelayMet</a></div>
<div><a href="#method-_mouseStart">_mouseStart</a></div>
<div><a href="#method-_mouseDrag">_mouseDrag</a></div>
<div><a href="#method-_mouseStop">_mouseStop</a></div>
<div><a href="#method-_mouseCapture">_mouseCapture</a></div>
</div>
<div class="quick-nav-section"><h3>Events</h3></div></section><div class="longdesc" id="entry-longdesc">
		<p>Similar to <a href="/jQuery.Widget#jQuery-Widget2"><code>jQuery.Widget</code></a>, the mouse interaction is not intended to be used directly. It is purely a base layer for other widgets to inherit from. This page only documents what is added to <code>jQuery.Widget</code>, but it does include internal methods that are not intended to be overwritten. The intended public API is <a href="#method-_mouseStart"><code>_mouseStart()</code></a>, <a href="#method-_mouseDrag"><code>_mouseDrag()</code></a>, <a href="#method-_mouseStop"><code>_mouseStop()</code></a>, and <a href="#method-_mouseCapture"><code>_mouseCapture()</code></a>.</p>
	</div>
<section id="options"><header><h2 class="underline">Options</h2></header><div id="option-cancel" class="api-item first-item">
<h3>cancel<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Selector">Selector</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>"input,textarea,button,select,option"</code>
</div>
<div>Prevents interactions from starting on specified elements.</div>
<strong>Code examples:</strong><p>Initialize the jQuery.ui.mouse with the cancel option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).jQuery.ui.mouse({ cancel: </code><code class="string">".title"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the cancel option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">cancel = $( </code><code class="string">".selector"</code> <code class="plain">).jQuery.ui.mouse( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"cancel"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).jQuery.ui.mouse( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"cancel"</code><code class="plain">, </code><code class="string">".title"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-delay" class="api-item">
<h3>delay<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Number">Number</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>0</code>
</div>
<div>Time in milliseconds after mousedown until the interaction should start. This option can be used to prevent unwanted interactions when clicking on an element.</div>
<strong>Code examples:</strong><p>Initialize the jQuery.ui.mouse with the delay option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).jQuery.ui.mouse({ delay: 300 });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the delay option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">delay = $( </code><code class="string">".selector"</code> <code class="plain">).jQuery.ui.mouse( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"delay"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).jQuery.ui.mouse( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"delay"</code><code class="plain">, 300 );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-distance" class="api-item">
<h3>distance<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Number">Number</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>1</code>
</div>
<div>Distance in pixels after mousedown the mouse must move before the interaction should start. This option can be used to prevent unwanted interactions when clicking on an element.</div>
<strong>Code examples:</strong><p>Initialize the jQuery.ui.mouse with the distance option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).jQuery.ui.mouse({ distance: 10 });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the distance option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">distance = $( </code><code class="string">".selector"</code> <code class="plain">).jQuery.ui.mouse( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"distance"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).jQuery.ui.mouse( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"distance"</code><code class="plain">, 10 );</code></div></div></td></tr></tbody></table></div>
</div></section><section id="methods"><header><h2 class="underline">Methods</h2></header><div id="method-_mouseCapture"><div class="api-item first-item">
<h3>_mouseCapture()<span class="returns">Returns: <a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div>
				Determines whether an interaction should start based on event target of the interaction. The default implementation always returns <code>true</code>.
			</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the _mouseCapture method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).jQuery.ui.mouse( </code><code class="string">"_mouseCapture"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-_mouseDelayMet"><div class="api-item">
<h3>_mouseDelayMet()<span class="returns">Returns: <a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div>
				Determines whether the <a href="#option-delay"><code>delay</code></a> option has been met for the current interaction.
			</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the _mouseDelayMet method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).jQuery.ui.mouse( </code><code class="string">"_mouseDelayMet"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-_mouseDestroy"><div class="api-item">
<h3>_mouseDestroy()</h3>
<div>
				Destroys the interaction event handlers. This must be called from the extending widget's <code>_destroy()</code> method.
			</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the _mouseDestroy method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).jQuery.ui.mouse( </code><code class="string">"_mouseDestroy"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-_mouseDistanceMet"><div class="api-item">
<h3>_mouseDistanceMet()<span class="returns">Returns: <a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div>
				Determines whether the <a href="#option-distance"><code>distance</code></a> option has been met for the current interaction.
			</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the _mouseDistanceMet method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).jQuery.ui.mouse( </code><code class="string">"_mouseDistanceMet"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-_mouseDown"><div class="api-item">
<h3>_mouseDown()</h3>
<div>
				Handles the beginning of an interaction. Verifies that the event is associated with the primary mouse button and ensures that the <a href="#option-delay"><code>delay</code></a> and <a href="#option-distance"><code>distance</code></a> options are met prior to starting the interaction. When the interaction is ready to start, invokes the <a href="#method-_mouseStart"><code>_mouseStart()</code></a> method for the extending widget to handle.
			</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the _mouseDown method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).jQuery.ui.mouse( </code><code class="string">"_mouseDown"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-_mouseDrag"><div class="api-item">
<h3>_mouseDrag()</h3>
<div>
				The extending widget should implement a <code>_mouseDrag()</code> method to handle each movement of an interaction. This method will receive the mouse event associated with the movement.
			</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the _mouseDrag method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).jQuery.ui.mouse( </code><code class="string">"_mouseDrag"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-_mouseInit"><div class="api-item">
<h3>_mouseInit()</h3>
<div>
				Initializes the interaction event handlers. This must be called from the extending widget's <code>_create()</code> method.
			</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the _mouseInit method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).jQuery.ui.mouse( </code><code class="string">"_mouseInit"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-_mouseMove"><div class="api-item">
<h3>_mouseMove()</h3>
<div>
				Handles each movement of the interaction. Invokes the <a href="#method-_mouseDrag"><code>mouseDrag()</code></a> method for the extending widget to handle.
			</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the _mouseMove method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).jQuery.ui.mouse( </code><code class="string">"_mouseMove"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-_mouseStart"><div class="api-item">
<h3>_mouseStart()</h3>
<div>
				The extending widget should implement a <code>_mouseStart()</code> method to handle the beginning of an interaction. This method will receive the mouse event associated with the start of the interaction.
			</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the _mouseStart method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).jQuery.ui.mouse( </code><code class="string">"_mouseStart"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-_mouseStop"><div class="api-item">
<h3>_mouseStop()</h3>
<div>
				The extending widget should implement a <code>_mouseStop()</code> method to handle the end of an interaction. This method will receive the mouse event associated with the end of the interaction.
			</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the _mouseStop method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).jQuery.ui.mouse( </code><code class="string">"_mouseStop"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-_mouseUp"><div class="api-item">
<h3>_mouseUp()</h3>
<div>
				Handles the end of the interaction. Invokes the <a href="#method-_mouseStop"><code>mouseStop()</code></a> method for the extending widget to handle.
			</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the _mouseUp method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).jQuery.ui.mouse( </code><code class="string">"_mouseUp"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div></section>
</div></article>

</body>
</html>
