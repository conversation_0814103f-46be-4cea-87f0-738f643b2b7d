<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>jQuery UI Droppable - Revert draggable position</title>
	<link rel="stylesheet" href="../../themes/base/jquery.ui.all.css">
	<script src="../../jquery-1.9.1.js"></script>
	<script src="../../ui/jquery.ui.core.js"></script>
	<script src="../../ui/jquery.ui.widget.js"></script>
	<script src="../../ui/jquery.ui.mouse.js"></script>
	<script src="../../ui/jquery.ui.draggable.js"></script>
	<script src="../../ui/jquery.ui.droppable.js"></script>
	<link rel="stylesheet" href="../demos.css">
	<style>
	#draggable, #draggable2 { width: 100px; height: 100px; padding: 0.5em; float: left; margin: 10px 10px 10px 0; }
	#droppable { width: 150px; height: 150px; padding: 0.5em; float: left; margin: 10px; }
	</style>
	<script>
	$(function() {
		$( "#draggable" ).draggable({ revert: "valid" });
		$( "#draggable2" ).draggable({ revert: "invalid" });

		$( "#droppable" ).droppable({
			activeClass: "ui-state-hover",
			hoverClass: "ui-state-active",
			drop: function( event, ui ) {
				$( this )
					.addClass( "ui-state-highlight" )
					.find( "p" )
						.html( "Dropped!" );
			}
		});
	});
	</script>
</head>
<body>

<div id="draggable" class="ui-widget-content">
	<p>I revert when I'm dropped</p>
</div>

<div id="draggable2" class="ui-widget-content">
	<p>I revert when I'm not dropped</p>
</div>

<div id="droppable" class="ui-widget-header">
	<p>Drop me here</p>
</div>

<div class="demo-description">
<p>Return the draggable (or it's helper) to its original location when dragging stops with the boolean <code>revert</code> option set on the draggable.</p>
</div>
</body>
</html>
