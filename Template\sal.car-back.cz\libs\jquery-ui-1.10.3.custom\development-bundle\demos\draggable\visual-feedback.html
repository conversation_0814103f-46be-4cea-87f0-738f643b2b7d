<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>jQuery UI Draggable - Visual feedback</title>
	<link rel="stylesheet" href="../../themes/base/jquery.ui.all.css">
	<script src="../../jquery-1.9.1.js"></script>
	<script src="../../ui/jquery.ui.core.js"></script>
	<script src="../../ui/jquery.ui.widget.js"></script>
	<script src="../../ui/jquery.ui.mouse.js"></script>
	<script src="../../ui/jquery.ui.draggable.js"></script>
	<link rel="stylesheet" href="../demos.css">
	<style>
	#draggable, #draggable2, #draggable3, #set div { width: 90px; height: 90px; padding: 0.5em; float: left; margin: 0 10px 10px 0; }
	#draggable, #draggable2, #draggable3 { margin-bottom:20px; }
	#set { clear:both; float:left; width: 368px; height: 120px; }
	p { clear:both; margin:0; padding:1em 0; }
	</style>
	<script>
	$(function() {
		$( "#draggable" ).draggable({ helper: "original" });
		$( "#draggable2" ).draggable({ opacity: 0.7, helper: "clone" });
		$( "#draggable3" ).draggable({
			cursor: "move",
			cursorAt: { top: -12, left: -20 },
			helper: function( event ) {
				return $( "<div class='ui-widget-header'>I'm a custom helper</div>" );
			}
		});
		$( "#set div" ).draggable({ stack: "#set div" });
	});
	</script>
</head>
<body>

<h3 class="docs">With helpers:</h3>

<div id="draggable" class="ui-widget-content">
	<p>Original</p>
</div>

<div id="draggable2" class="ui-widget-content">
	<p>Semi-transparent clone</p>
</div>

<div id="draggable3" class="ui-widget-content">
	<p>Custom helper (in combination with cursorAt)</p>
</div>

<h3 class="docs">Stacked:</h3>
<div id="set">
	<div class="ui-widget-content">
		<p>We are draggables..</p>
	</div>

	<div class="ui-widget-content">
		<p>..whose z-indexes are controlled automatically..</p>
	</div>

	<div class="ui-widget-content">
		<p>..with the stack option.</p>
	</div>
</div>

<div class="demo-description">
<p>Provide feedback to users as they drag an object in the form of a helper. The <code>helper</code> option accepts the values 'original' (the draggable object moves with the cursor), 'clone' (a duplicate of the draggable moves with the cursor), or a function that returns a DOM element (that element is shown near the cursor during drag). Control the helper's transparency with the <code>opacity</code> option.</p>
<p>To clarify which draggable is in play, bring the draggable in motion to front. Use the <code>zIndex</code> option to set a higher z-index for the helper, if in play, or use the <code>stack</code> option to ensure that the last item dragged will appear on top of others in the same group on drag stop.</p>
</div>
</body>
</html>
