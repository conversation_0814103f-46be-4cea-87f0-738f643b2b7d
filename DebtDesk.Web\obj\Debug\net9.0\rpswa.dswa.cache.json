{"GlobalPropertiesHash": "wCc/aiGBLVbkpMbIXNToUmkXnS1JEYI+HmwxKxLUR7k=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["0zpbrNTSGjReyMOKgsVUCRP9jObBZ6nEq27WGWsW+LU=", "jIMMNX3Up71Fi6ZpHaqeQjwWg+liw/X0ankjFD/ROzc=", "vitPlGeVt/ByCqAp1FKkFZIVfPBRgF2m/G1D3wNlleI=", "y2rDqTonyBTOu1K8tOFirfYYwd3RMBUfhd8oOU497lc=", "dX4lK6cqIEZzh/ecJ0G2Ho81RZ0cHLfjebTVV/KSAk0=", "D7OswGnojKK7sJA9ObqBv0OkL5cUogh+3rce1tGtsBg=", "A3/SLpt33wcEmcLHxLIjiripR63x5f+sZAFCGoHKWwU=", "ddMz9grvNmFGvniE3Bc12eYpr4iPykwbqbG9pdsos38=", "dXdlm5DXnOdLQ/w/WJNFzYQ4nKaYjytDXIz0Mgqoq/I=", "wDdm7R4ya+FUtvXNTLWqT0Dtqz0T3ojn3ZWYLND2n8A=", "+zuP0YAaPdKGHzSR2fg2gmhWwQZajiLubWh16p+b25I=", "TMU///aGIxWstoM2YoWLgb7w4uL0891kF8tDekNi6tA=", "kF2bz069nMh2yyCjRST+p9ZQ4tZmpJ51FRP0KZd4efc=", "S8HYXgPNa0R48nHVrUB9Ws7grcTAGe2iGZF87YPLigg=", "vyPm5MpO0dmRP8hFrCn21tL+f3ct+N2bAe390JMLkNQ=", "0Xs51zLdL68csx4CFI1ZXjMn9VwYjGDqdSTYtvsHLXo=", "yN0kCufjxsBO6HAcwAy9ohI36Ubsg7zBbDLBSbj9Xks=", "14lXTQKqpiAUJCPuIZoGEHnvKLbPAiuOyocDRPX9q3E=", "OfoKPJUoaWcdiNrlCmtxbueyMhJDpAtCOZndxBuFDtg=", "EfqnNSvC0irx5CPqC0fqeUoGYIKTNWQkRyMwPkuRBC8=", "Cg/lwYxVKuX7njaC7hab4RwK6Df+LXyrzo8XeUmG2+Y=", "a7u/NftCZlCuFLNnHYSUh55A805A94a56FyBUYpbS34=", "1TzVA3Vbo5xr81S+Jwg8Ba23DswsCddeVTqm+5ouwvE=", "8XzVa3i7t1MXbNfiibM6AZ4O/hpJAmLFVkqOmeEyqZ8=", "YOWYy+bisodyLsMKpxvC3HH6V9fwG+dEBJOJDYgvhXc=", "k3sJSkLgIZQjFkhkbVPxnEkHgnh1HcvFsp6fKRuMVnQ=", "x8Xl/rN6HQRD3JkToWRji+goHr02vZfXbTVT7/vmvDg=", "JMLxb9k6ED4/X/oG9vfFljN3Pvjqvm46JFP+buk2evE=", "CvPCskCG5OiVHCMDf2utANk6C+AEfMt8L+VKfNvxsb0=", "cEALo1NTS8af5yhg14ZbLaalIqvxNkbe5OvBLZV/0Bs=", "8MuVDXbNS5Qr6h+nPvCyDP2pHb3YBb7qY01bjpU+X24=", "VaxTXlVsf/h5/d1HI+YFXguBdtS6gxou5KYVykpN99c=", "e3QXfiTNl6Hc6lzc5F/TKIFR7pqR69u3rRFrIV1hffU=", "TRQk/0Oc37oAOu73A0T6OKzT4c6n7kt7XF6qpRzWf6Q=", "ERiqTtdkask+ovC149cz3BnPllW3s1iNo8h8UlYPOxE=", "CkZfU9thBkR3as3QomKi9sLVVpTicGqlh3xWYS/U8aM=", "Guvw1IujdlNG1sGsECVuev8pbaKO/JejQCx8UXCgX58=", "YVjBnxwae+1SMA+vPpvA6NFsU/r8Ung6oCV57s8sHY0=", "LSYDTURRdYswPp1/2CkHMcSY0Ai5ElaRoIOCZiOObR4=", "FVT5iJzuZ2IKqlEm5hZuz0zUzBQ6lqABrhp3Yh+WhXQ=", "V9G7Dw+Uv6CKVr/wbfUEG4aqjgrAzERHGtg7ow9YmYk=", "r/1GfZaMq6OQBHb0rpgdxOOV0LYYR9wlsdLlXt6FGUo=", "pCLI1BkKne6IQB8QqdWqGIR0WzY6hp+thq1ULRIOoUg=", "3uLf/8/II3puyuI5hoq5VnGN4FJY3tsBvgihWWh29Ug=", "q9kH/DfPKi3LVFTjIskvlXrqHdZOMBVqJQrVzrgnHyc=", "hJrXf18DhYPHOYnVSJag9eCSwmFriT3IVSwm16Bs0dA=", "elPh0MjzIPuXih5fNHygn620hY+dl61dHa+xNEQyOMY=", "MrxDwMmYwg6HGujz0qh6vrh1PVq+9xXzoCUj49n2n+c=", "IWG2pm3v20tHpFtUTObyp/EteVhnEYAs5De+DM18pFs=", "0KQcPJdERKwqXdZvMYKXlnY9Vd6F/TuxYzH6oV1cP8k=", "2IcJb9uHUQE3LjWP7b/FDxy/DnB712SVy34lhlEZOiY=", "9F29xDZYUmkXfxRnIwFNy9hSz3BV1FcqQznWH78Rt9Y=", "Mkcb3gVUi6LXl1uX5pM3FnSKemDhZwXniLJz8WPuVqU=", "oEnW4lnkrNfnljtLNGZkIGszsgvv9Dhkjyrbi71TvlM=", "XNd+FhhcPJgPsxWcis7SVHObDJPa/AwsHXps79+hswg=", "SWhPC8JfykN/gM+VkVv7Xp5+kDappRiKedy3wS6R0pY=", "xUvWQOE/y0AuENtTD7dmGMZUSyij0ggmiBt+jncSumE=", "g4AJskwktrIGFmvNnuy/zZbFNCuz6P3JWwVfUJ1DtBI=", "uc91J9DT8z0HYYBqKiQg+A1jUaplZEJJoI7y5cqpYN8=", "ZA843Ysk6Mzq/vR543pVyP4QmRg8hLtx52QUPW4dhOo=", "uTMzzR0yfRGo9GqTaMzNvcHz4ef/y2mfrXV81GjYPfE=", "eclSPzsfz7Yg1U7nBeVpQt66f1x3VNbUcn1ZvAXIz4I=", "EpdzSfi1X+MEOMOlflP2ELkKoZh4H7KFOgxohyCnp0w=", "jQy2ExImYxqtkK7uXp/JcF6mwdicXiRVTKazJMNYvvw=", "i5kP7rS9FhRqkusV9c+FVSGBq04TFVITlZtsn0ezyd4=", "kmnR6eSgms1tSUwAZLJ0VmV3o2GRlTQSFYU7NBi31ss=", "3E5waMx3IJRpCR3tNhRp/+bUkCLVo4FWc3GlYJK0Efk=", "do16Cm8qDiAOVm+3+pHhoTAFTrnSCKDowITMF8UsK2w=", "aPXJ0jV8cetZ2nfYq8Nx+y9io0dxc8ZN+ABWiMz4Bww=", "abCaEJChRd3c0VQOZnIU+yUrAifCvRl/qonJ9eHj76A=", "7rBiqunYj8WBVpzVZ1g8ICtP4vj1rTKLOeBuGwoVt70=", "ecffc/hQxVom/RdbrbCbjlbB+XT8nzGF3T9mn2BGuqg=", "bKNH5pXfgluhZtwJxefFSQS8EexjuMc+AsDSXY1d1nk=", "6TOE09ngNBDKE/T/fYXnIGsjBuHEu0hThvC988JMftA=", "c3derEsIOVuOcE0oYVZWP6QBbfaXn1HDk9Hbum8Qbd4="], "CachedAssets": {"0zpbrNTSGjReyMOKgsVUCRP9jObBZ6nEq27WGWsW+LU=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\css\\site.css", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b9sayid5wm", "Integrity": "j6fhJSuuyLpOSLuPJU0TsDV0iNjor5S3rDnvxJrt4bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 667, "LastWriteTime": "2025-07-09T13:11:15.6673089+00:00"}, "jIMMNX3Up71Fi6ZpHaqeQjwWg+liw/X0ankjFD/ROzc=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\favicon.ico", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-07-09T13:11:15.6257919+00:00"}, "vitPlGeVt/ByCqAp1FKkFZIVfPBRgF2m/G1D3wNlleI=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\js\\site.js", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-07-09T13:11:15.6683086+00:00"}, "y2rDqTonyBTOu1K8tOFirfYYwd3RMBUfhd8oOU497lc=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-07-09T13:11:15.538273+00:00"}, "dX4lK6cqIEZzh/ecJ0G2Ho81RZ0cHLfjebTVV/KSAk0=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-07-09T13:11:15.5392729+00:00"}, "D7OswGnojKK7sJA9ObqBv0OkL5cUogh+3rce1tGtsBg=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-07-09T13:11:15.5402698+00:00"}, "A3/SLpt33wcEmcLHxLIjiripR63x5f+sZAFCGoHKWwU=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-07-09T13:11:15.5402698+00:00"}, "ddMz9grvNmFGvniE3Bc12eYpr4iPykwbqbG9pdsos38=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-07-09T13:11:15.5412703+00:00"}, "dXdlm5DXnOdLQ/w/WJNFzYQ4nKaYjytDXIz0Mgqoq/I=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-07-09T13:11:15.5422706+00:00"}, "wDdm7R4ya+FUtvXNTLWqT0Dtqz0T3ojn3ZWYLND2n8A=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-07-09T13:11:15.5432698+00:00"}, "+zuP0YAaPdKGHzSR2fg2gmhWwQZajiLubWh16p+b25I=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-07-09T13:11:15.5432698+00:00"}, "TMU///aGIxWstoM2YoWLgb7w4uL0891kF8tDekNi6tA=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-07-09T13:11:15.5432698+00:00"}, "kF2bz069nMh2yyCjRST+p9ZQ4tZmpJ51FRP0KZd4efc=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-07-09T13:11:15.5442694+00:00"}, "S8HYXgPNa0R48nHVrUB9Ws7grcTAGe2iGZF87YPLigg=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-07-09T13:11:15.5452689+00:00"}, "vyPm5MpO0dmRP8hFrCn21tL+f3ct+N2bAe390JMLkNQ=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-07-09T13:11:15.5462707+00:00"}, "0Xs51zLdL68csx4CFI1ZXjMn9VwYjGDqdSTYtvsHLXo=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-07-09T13:11:15.5462707+00:00"}, "yN0kCufjxsBO6HAcwAy9ohI36Ubsg7zBbDLBSbj9Xks=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-07-09T13:11:15.5472697+00:00"}, "14lXTQKqpiAUJCPuIZoGEHnvKLbPAiuOyocDRPX9q3E=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-07-09T13:11:15.5472697+00:00"}, "OfoKPJUoaWcdiNrlCmtxbueyMhJDpAtCOZndxBuFDtg=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-07-09T13:11:15.5482712+00:00"}, "EfqnNSvC0irx5CPqC0fqeUoGYIKTNWQkRyMwPkuRBC8=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-07-09T13:11:15.5492706+00:00"}, "Cg/lwYxVKuX7njaC7hab4RwK6Df+LXyrzo8XeUmG2+Y=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-07-09T13:11:15.5502711+00:00"}, "a7u/NftCZlCuFLNnHYSUh55A805A94a56FyBUYpbS34=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-07-09T13:11:15.5502711+00:00"}, "1TzVA3Vbo5xr81S+Jwg8Ba23DswsCddeVTqm+5ouwvE=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-07-09T13:11:15.5512709+00:00"}, "8XzVa3i7t1MXbNfiibM6AZ4O/hpJAmLFVkqOmeEyqZ8=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-07-09T13:11:15.5537908+00:00"}, "YOWYy+bisodyLsMKpxvC3HH6V9fwG+dEBJOJDYgvhXc=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-07-09T13:11:15.5547896+00:00"}, "k3sJSkLgIZQjFkhkbVPxnEkHgnh1HcvFsp6fKRuMVnQ=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-07-09T13:11:15.5557889+00:00"}, "x8Xl/rN6HQRD3JkToWRji+goHr02vZfXbTVT7/vmvDg=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-07-09T13:11:15.5567898+00:00"}, "JMLxb9k6ED4/X/oG9vfFljN3Pvjqvm46JFP+buk2evE=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-07-09T13:11:15.5587885+00:00"}, "CvPCskCG5OiVHCMDf2utANk6C+AEfMt8L+VKfNvxsb0=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-07-09T13:11:15.5617904+00:00"}, "cEALo1NTS8af5yhg14ZbLaalIqvxNkbe5OvBLZV/0Bs=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-07-09T13:11:15.5637914+00:00"}, "8MuVDXbNS5Qr6h+nPvCyDP2pHb3YBb7qY01bjpU+X24=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-07-09T13:11:15.5657898+00:00"}, "VaxTXlVsf/h5/d1HI+YFXguBdtS6gxou5KYVykpN99c=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-07-09T13:11:15.566789+00:00"}, "e3QXfiTNl6Hc6lzc5F/TKIFR7pqR69u3rRFrIV1hffU=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-07-09T13:11:15.5697925+00:00"}, "TRQk/0Oc37oAOu73A0T6OKzT4c6n7kt7XF6qpRzWf6Q=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-07-09T13:11:15.5717896+00:00"}, "ERiqTtdkask+ovC149cz3BnPllW3s1iNo8h8UlYPOxE=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-07-09T13:11:15.5747874+00:00"}, "CkZfU9thBkR3as3QomKi9sLVVpTicGqlh3xWYS/U8aM=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-07-09T13:11:15.5777919+00:00"}, "Guvw1IujdlNG1sGsECVuev8pbaKO/JejQCx8UXCgX58=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-07-09T13:11:15.5787883+00:00"}, "YVjBnxwae+1SMA+vPpvA6NFsU/r8Ung6oCV57s8sHY0=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-07-09T13:11:15.5797902+00:00"}, "LSYDTURRdYswPp1/2CkHMcSY0Ai5ElaRoIOCZiOObR4=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-07-09T13:11:15.5817896+00:00"}, "FVT5iJzuZ2IKqlEm5hZuz0zUzBQ6lqABrhp3Yh+WhXQ=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-07-09T13:11:15.5827896+00:00"}, "V9G7Dw+Uv6CKVr/wbfUEG4aqjgrAzERHGtg7ow9YmYk=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-07-09T13:11:15.5837894+00:00"}, "r/1GfZaMq6OQBHb0rpgdxOOV0LYYR9wlsdLlXt6FGUo=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-07-09T13:11:15.5847898+00:00"}, "pCLI1BkKne6IQB8QqdWqGIR0WzY6hp+thq1ULRIOoUg=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-07-09T13:11:15.5857893+00:00"}, "3uLf/8/II3puyuI5hoq5VnGN4FJY3tsBvgihWWh29Ug=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-07-09T13:11:15.5867887+00:00"}, "q9kH/DfPKi3LVFTjIskvlXrqHdZOMBVqJQrVzrgnHyc=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-07-09T13:11:15.5877898+00:00"}, "hJrXf18DhYPHOYnVSJag9eCSwmFriT3IVSwm16Bs0dA=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-07-09T13:11:15.5887907+00:00"}, "elPh0MjzIPuXih5fNHygn620hY+dl61dHa+xNEQyOMY=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-07-09T13:11:15.5897883+00:00"}, "MrxDwMmYwg6HGujz0qh6vrh1PVq+9xXzoCUj49n2n+c=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-07-09T13:11:15.5677885+00:00"}, "IWG2pm3v20tHpFtUTObyp/EteVhnEYAs5De+DM18pFs=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-07-09T13:11:15.6287869+00:00"}, "0KQcPJdERKwqXdZvMYKXlnY9Vd6F/TuxYzH6oV1cP8k=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-07-09T13:11:15.6307884+00:00"}, "2IcJb9uHUQE3LjWP7b/FDxy/DnB712SVy34lhlEZOiY=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-07-09T13:11:15.575789+00:00"}, "9F29xDZYUmkXfxRnIwFNy9hSz3BV1FcqQznWH78Rt9Y=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-07-09T13:11:15.6207896+00:00"}, "Mkcb3gVUi6LXl1uX5pM3FnSKemDhZwXniLJz8WPuVqU=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-07-09T13:11:15.6247884+00:00"}, "oEnW4lnkrNfnljtLNGZkIGszsgvv9Dhkjyrbi71TvlM=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-07-09T13:11:15.6267882+00:00"}, "XNd+FhhcPJgPsxWcis7SVHObDJPa/AwsHXps79+hswg=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-07-09T13:11:15.6277925+00:00"}, "SWhPC8JfykN/gM+VkVv7Xp5+kDappRiKedy3wS6R0pY=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-07-09T13:11:15.5727887+00:00"}, "xUvWQOE/y0AuENtTD7dmGMZUSyij0ggmiBt+jncSumE=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 285314, "LastWriteTime": "2025-07-09T13:11:15.5907928+00:00"}, "g4AJskwktrIGFmvNnuy/zZbFNCuz6P3JWwVfUJ1DtBI=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-07-09T13:11:15.5927876+00:00"}, "uc91J9DT8z0HYYBqKiQg+A1jUaplZEJJoI7y5cqpYN8=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-07-09T13:11:15.5977914+00:00"}, "ZA843Ysk6Mzq/vR543pVyP4QmRg8hLtx52QUPW4dhOo=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-07-09T13:11:15.5987868+00:00"}, "uTMzzR0yfRGo9GqTaMzNvcHz4ef/y2mfrXV81GjYPfE=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-07-09T13:11:15.6007873+00:00"}, "eclSPzsfz7Yg1U7nBeVpQt66f1x3VNbUcn1ZvAXIz4I=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-07-09T13:11:15.6167874+00:00"}, "EpdzSfi1X+MEOMOlflP2ELkKoZh4H7KFOgxohyCnp0w=": {"Identity": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "DebtDesk.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\wwwroot\\", "BasePath": "_content/DebtDesk.Web", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-07-09T13:11:15.5707907+00:00"}}, "CachedCopyCandidates": {}}