{"format": 1, "restore": {"C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\DebtDesk.Web.csproj": {}}, "projects": {"C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Core\\DebtDesk.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Core\\DebtDesk.Core.csproj", "projectName": "DebtDesk.Core", "projectPath": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Core\\DebtDesk.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Infrastructure\\DebtDesk.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Infrastructure\\DebtDesk.Infrastructure.csproj", "projectName": "DebtDesk.Infrastructure", "projectPath": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Infrastructure\\DebtDesk.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Core\\DebtDesk.Core.csproj": {"projectPath": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Core\\DebtDesk.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\DebtDesk.Web.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\DebtDesk.Web.csproj", "projectName": "DebtDesk.Web", "projectPath": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\DebtDesk.Web.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Web\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Core\\DebtDesk.Core.csproj": {"projectPath": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Core\\DebtDesk.Core.csproj"}, "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Infrastructure\\DebtDesk.Infrastructure.csproj": {"projectPath": "C:\\Work\\Other\\CoolWorks\\debtdesk\\DebtDesk.Infrastructure\\DebtDesk.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}