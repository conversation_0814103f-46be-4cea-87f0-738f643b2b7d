using System.ComponentModel.DataAnnotations;

namespace DebtDesk.Core.Entities
{
    public class DebtType : BaseEntity
    {
        [Required]
        [MaxLength(50)]
        public string Text { get; set; } = string.Empty;

        public int Amount { get; set; }

        [MaxLength(10)]
        public string Method { get; set; } = "Absolute";

        public bool Active { get; set; } = true;

        [MaxLength(50)]
        public string Filter { get; set; } = "DLU";

        // Navigation properties
        public virtual ICollection<DebtCase> DebtCases { get; set; } = new List<DebtCase>();
    }
}
