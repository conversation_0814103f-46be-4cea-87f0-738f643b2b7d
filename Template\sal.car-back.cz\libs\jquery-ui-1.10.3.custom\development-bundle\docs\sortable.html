<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>jQuery UI sortable documentation</title>

	<style>
	body {
		font-family: "Trebuchet MS", "Arial", "Helvetica", "Verdana", "sans-serif"
	}
	.gutter {
		display: none;
	}
	</style>
</head>
<body>

<script>{
		"title":
			"Sortable Widget",
		"excerpt":
			"Reorder elements in a list or grid using the mouse.",
		"termSlugs": {
			"category": [
				"interactions"
			]
		}
	}</script><article id="sortable1" class="entry widget"><h2 class="section-title">
<span>Sortable Widget</span><span class="version-details">version added: 1.0</span>
</h2>
<div class="entry-wrapper">
<p class="desc"><strong>Description: </strong>Reorder elements in a list or grid using the mouse.</p>
<section id="quick-nav"><header><h2>QuickNav<a href="#entry-examples">Examples</a>
</h2></header><div class="quick-nav-section">
<h3>Options</h3>
<div><a href="#option-appendTo">appendTo</a></div>
<div><a href="#option-axis">axis</a></div>
<div><a href="#option-cancel">cancel</a></div>
<div><a href="#option-connectWith">connectWith</a></div>
<div><a href="#option-containment">containment</a></div>
<div><a href="#option-cursor">cursor</a></div>
<div><a href="#option-cursorAt">cursorAt</a></div>
<div><a href="#option-delay">delay</a></div>
<div><a href="#option-disabled">disabled</a></div>
<div><a href="#option-distance">distance</a></div>
<div><a href="#option-dropOnEmpty">dropOnEmpty</a></div>
<div><a href="#option-forceHelperSize">forceHelperSize</a></div>
<div><a href="#option-forcePlaceholderSize">forcePlaceholderSize</a></div>
<div><a href="#option-grid">grid</a></div>
<div><a href="#option-handle">handle</a></div>
<div><a href="#option-helper">helper</a></div>
<div><a href="#option-items">items</a></div>
<div><a href="#option-opacity">opacity</a></div>
<div><a href="#option-placeholder">placeholder</a></div>
<div><a href="#option-revert">revert</a></div>
<div><a href="#option-scroll">scroll</a></div>
<div><a href="#option-scrollSensitivity">scrollSensitivity</a></div>
<div><a href="#option-scrollSpeed">scrollSpeed</a></div>
<div><a href="#option-tolerance">tolerance</a></div>
<div><a href="#option-zIndex">zIndex</a></div>
</div>
<div class="quick-nav-section">
<h3>Methods</h3>
<div><a href="#method-cancel">cancel</a></div>
<div><a href="#method-destroy">destroy</a></div>
<div><a href="#method-disable">disable</a></div>
<div><a href="#method-enable">enable</a></div>
<div><a href="#method-option">option</a></div>
<div><a href="#method-refresh">refresh</a></div>
<div><a href="#method-refreshPositions">refreshPositions</a></div>
<div><a href="#method-serialize">serialize</a></div>
<div><a href="#method-toArray">toArray</a></div>
<div><a href="#method-widget">widget</a></div>
</div>
<div class="quick-nav-section">
<h3>Events</h3>
<div><a href="#event-create">create</a></div>
<div><a href="#event-start">start</a></div>
<div><a href="#event-sort">sort</a></div>
<div><a href="#event-change">change</a></div>
<div><a href="#event-beforeStop">beforeStop</a></div>
<div><a href="#event-stop">stop</a></div>
<div><a href="#event-update">update</a></div>
<div><a href="#event-receive">receive</a></div>
<div><a href="#event-remove">remove</a></div>
<div><a href="#event-over">over</a></div>
<div><a href="#event-out">out</a></div>
<div><a href="#event-activate">activate</a></div>
<div><a href="#event-deactivate">deactivate</a></div>
</div></section><div class="longdesc" id="entry-longdesc">
		<p>The jQuery UI Sortable plugin makes selected elements sortable by dragging with the mouse.</p>
		<p><em>Note: In order to sort table rows, the <code>tbody</code> must be made sortable, not the <code>table</code>.</em></p>
	</div>
<section id="options"><header><h2 class="underline">Options</h2></header><div id="option-appendTo" class="api-item first-item">
<h3>appendTo<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#jQuery">jQuery</a> or <a href="http://api.jquery.com/Types#Element">Element</a> or <a href="http://api.jquery.com/Types#Selector">Selector</a> or <a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>"parent"</code>
</div>
<div>Defines where the helper that moves with the mouse is being appended to during the drag (for example, to resolve overlap/zIndex issues).</div>
<strong>Multiple types supported:</strong><ul>
<li>
<strong>jQuery</strong>: A jQuery object containing the element to append the helper to.</li>
<li>
<strong>Element</strong>: The element to append the helper to.</li>
<li>
<strong>Selector</strong>: A selector specifying which element to append the helper to.</li>
<li>
<strong>String</strong>: The string <code>"parent"</code> will cause the helper to be a sibling of the sortable item.</li>
</ul>
<strong>Code examples:</strong><p>Initialize the sortable with the appendTo option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({ appendTo: document.body });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the appendTo option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">appendTo = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"appendTo"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"appendTo"</code><code class="plain">, document.body );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-axis" class="api-item">
<h3>axis<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>If defined, the items can be dragged only horizontally or vertically. Possible values: <code>"x"</code>, <code>"y"</code>.</div>
<strong>Code examples:</strong><p>Initialize the sortable with the axis option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({ axis: </code><code class="string">"x"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the axis option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">axis = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"axis"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"axis"</code><code class="plain">, </code><code class="string">"x"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-cancel" class="api-item">
<h3>cancel<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Selector">Selector</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>":input,button"</code>
</div>
<div>Prevents sorting if you start on elements matching the selector.</div>
<strong>Code examples:</strong><p>Initialize the sortable with the cancel option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({ cancel: </code><code class="string">"a,button"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the cancel option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">cancel = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"cancel"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"cancel"</code><code class="plain">, </code><code class="string">"a,button"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-connectWith" class="api-item">
<h3>connectWith<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Selector">Selector</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>A selector of other sortable elements that the items from this list should be connected to. This is a one-way relationship, if you want the items to be connected in both directions, the <code>connectWith</code> option must be set on both sortable elements.</div>
<strong>Code examples:</strong><p>Initialize the sortable with the connectWith option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({ connectWith: </code><code class="string">"#shopping-cart"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the connectWith option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">connectWith = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"connectWith"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"connectWith"</code><code class="plain">, </code><code class="string">"#shopping-cart"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-containment" class="api-item">
<h3>containment<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Element">Element</a> or <a href="http://api.jquery.com/Types#Selector">Selector</a> or <a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>
				<p>Defines a bounding box that the sortable items are contrained to while dragging.</p>

				<p>Note: The element specified for containment must have a calculated width and height (though it need not be explicit). For example, if you have <code>float: left</code> sortable children and specify <code>containment: "parent"</code> be sure to have <code>float: left</code> on the sortable/parent container as well or it will have <code>height: 0</code>, causing undefined behavior.</p>
			</div>
<strong>Multiple types supported:</strong><ul>
<li>
<strong>Element</strong>: An element to use as the container.</li>
<li>
<strong>Selector</strong>: A selector specifying an element to use as the container.</li>
<li>
<strong>String</strong>: A string identifying an element to use as the container. Possible values: <code>"parent"</code>, <code>"document"</code>, <code>"window"</code>.</li>
</ul>
<strong>Code examples:</strong><p>Initialize the sortable with the containment option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({ containment: </code><code class="string">"parent"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the containment option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">containment = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"containment"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"containment"</code><code class="plain">, </code><code class="string">"parent"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-cursor" class="api-item">
<h3>cursor<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>"auto"</code>
</div>
<div>Defines the cursor that is being shown while sorting.</div>
<strong>Code examples:</strong><p>Initialize the sortable with the cursor option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({ cursor: </code><code class="string">"move"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the cursor option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">cursor = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"cursor"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"cursor"</code><code class="plain">, </code><code class="string">"move"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-cursorAt" class="api-item">
<h3>cursorAt<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Object">Object</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Moves the sorting element or helper so the cursor always appears to drag from the same position. Coordinates can be given as a hash using a combination of one or two keys: <code>{ top, left, right, bottom }</code>.</div>
<strong>Code examples:</strong><p>Initialize the sortable with the cursorAt option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({ cursorAt: { left: 5 } });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the cursorAt option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">cursorAt = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"cursorAt"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"cursorAt"</code><code class="plain">, { left: 5 } );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-delay" class="api-item">
<h3>delay<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Integer">Integer</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>0</code>
</div>
<div>Time in milliseconds to define when the sorting should start. Adding a delay helps preventing unwanted drags when clicking on an element.</div>
<strong>Code examples:</strong><p>Initialize the sortable with the delay option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({ delay: 150 });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the delay option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">delay = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"delay"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"delay"</code><code class="plain">, 150 );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-disabled" class="api-item">
<h3>disabled<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Disables the sortable if set to <code>true</code>.</div>
<strong>Code examples:</strong><p>Initialize the sortable with the disabled option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({ disabled: </code><code class="keyword">true</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the disabled option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">disabled = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"disabled"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"disabled"</code><code class="plain">, </code><code class="keyword">true</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-distance" class="api-item">
<h3>distance<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Number">Number</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>1</code>
</div>
<div>Tolerance, in pixels, for when sorting should start. If specified, sorting will not start until after mouse is dragged beyond distance. Can be used to allow for clicks on elements within a handle.</div>
<strong>Code examples:</strong><p>Initialize the sortable with the distance option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({ distance: 5 });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the distance option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">distance = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"distance"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"distance"</code><code class="plain">, 5 );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-dropOnEmpty" class="api-item">
<h3>dropOnEmpty<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>true</code>
</div>
<div>If <code>false</code>, items from this sortable can't be dropped on an empty connect sortable (see the <a href="#option-connectWith"><code>connectWith</code></a> option.</div>
<strong>Code examples:</strong><p>Initialize the sortable with the dropOnEmpty option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({ dropOnEmpty: </code><code class="keyword">false</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the dropOnEmpty option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">dropOnEmpty = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"dropOnEmpty"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"dropOnEmpty"</code><code class="plain">, </code><code class="keyword">false</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-forceHelperSize" class="api-item">
<h3>forceHelperSize<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>If <code>true</code>, forces the helper to have a size.</div>
<strong>Code examples:</strong><p>Initialize the sortable with the forceHelperSize option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({ forceHelperSize: </code><code class="keyword">true</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the forceHelperSize option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">forceHelperSize = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"forceHelperSize"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"forceHelperSize"</code><code class="plain">, </code><code class="keyword">true</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-forcePlaceholderSize" class="api-item">
<h3>forcePlaceholderSize<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>If true, forces the placeholder to have a size.</div>
<strong>Code examples:</strong><p>Initialize the sortable with the forcePlaceholderSize option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({ forcePlaceholderSize: </code><code class="keyword">true</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the forcePlaceholderSize option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">forcePlaceholderSize = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"forcePlaceholderSize"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"forcePlaceholderSize"</code><code class="plain">, </code><code class="keyword">true</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-grid" class="api-item">
<h3>grid<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Array">Array</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Snaps the sorting element or helper to a grid, every x and y pixels. Array values: <code>[ x, y ]</code>.</div>
<strong>Code examples:</strong><p>Initialize the sortable with the grid option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({ grid: [ 20, 10 ] });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the grid option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">grid = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"grid"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"grid"</code><code class="plain">, [ 20, 10 ] );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-handle" class="api-item">
<h3>handle<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Selector">Selector</a> or <a href="http://api.jquery.com/Types#Element">Element</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Restricts sort start click to the specified element.</div>
<strong>Code examples:</strong><p>Initialize the sortable with the handle option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({ handle: </code><code class="string">".handle"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the handle option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">handle = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"handle"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"handle"</code><code class="plain">, </code><code class="string">".handle"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-helper" class="api-item">
<h3>helper<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#String">String</a> or <a href="http://api.jquery.com/Types/#Function">Function</a>()</span>
</h3>
<div class="default">
<strong>Default: </strong><code>"original"</code>
</div>
<div>Allows for a helper element to be used for dragging display.</div>
<strong>Multiple types supported:</strong><ul>
<li>
<strong>String</strong>: If set to <code>"clone"</code>, then the element will be cloned and the clone will be dragged.</li>
<li>
<strong>Function</strong>: A function that will return a DOMElement to use while dragging. The function receives the event and the element being sorted.</li>
</ul>
<strong>Code examples:</strong><p>Initialize the sortable with the helper option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({ helper: </code><code class="string">"clone"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the helper option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">helper = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"helper"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"helper"</code><code class="plain">, </code><code class="string">"clone"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-items" class="api-item">
<h3>items<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Selector">Selector</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>"&gt; *"</code>
</div>
<div>Specifies which items inside the element should be sortable.</div>
<strong>Code examples:</strong><p>Initialize the sortable with the items option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({ items: </code><code class="string">"&gt; li"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the items option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">items = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"items"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"items"</code><code class="plain">, </code><code class="string">"&gt; li"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-opacity" class="api-item">
<h3>opacity<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Number">Number</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Defines the opacity of the helper while sorting. From <code>0.01</code> to <code>1</code>.</div>
<strong>Code examples:</strong><p>Initialize the sortable with the opacity option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({ opacity: 0.5 });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the opacity option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">opacity = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"opacity"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"opacity"</code><code class="plain">, 0.5 );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-placeholder" class="api-item">
<h3>placeholder<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>A class name that gets applied to the otherwise white space.</div>
<strong>Code examples:</strong><p>Initialize the sortable with the placeholder option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({ placeholder: </code><code class="string">"sortable-placeholder"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the placeholder option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">placeholder = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"placeholder"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"placeholder"</code><code class="plain">, </code><code class="string">"sortable-placeholder"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-revert" class="api-item">
<h3>revert<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a> or <a href="http://api.jquery.com/Types#Number">Number</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Whether the sortable items should revert to their new positions using a smooth animation.</div>
<strong>Multiple types supported:</strong><ul>
<li>
<strong>Boolean</strong>: When set to <code>true</code>, the items will animate with the default duration.</li>
<li>
<strong>Number</strong>: The duration for the animation, in milliseconds.</li>
</ul>
<strong>Code examples:</strong><p>Initialize the sortable with the revert option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({ revert: </code><code class="keyword">true</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the revert option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">revert = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"revert"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"revert"</code><code class="plain">, </code><code class="keyword">true</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-scroll" class="api-item">
<h3>scroll<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>true</code>
</div>
<div>If set to true, the page scrolls when coming to an edge.</div>
<strong>Code examples:</strong><p>Initialize the sortable with the scroll option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({ scroll: </code><code class="keyword">false</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the scroll option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">scroll = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"scroll"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"scroll"</code><code class="plain">, </code><code class="keyword">false</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-scrollSensitivity" class="api-item">
<h3>scrollSensitivity<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Number">Number</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>20</code>
</div>
<div>Defines how near the mouse must be to an edge to start scrolling.</div>
<strong>Code examples:</strong><p>Initialize the sortable with the scrollSensitivity option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({ scrollSensitivity: 10 });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the scrollSensitivity option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">scrollSensitivity = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"scrollSensitivity"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"scrollSensitivity"</code><code class="plain">, 10 );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-scrollSpeed" class="api-item">
<h3>scrollSpeed<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Number">Number</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>20</code>
</div>
<div>The speed at which the window should scroll once the mouse pointer gets within the <a href="#option-scrollSensitivity"><code>scrollSensitivity</code></a> distance.</div>
<strong>Code examples:</strong><p>Initialize the sortable with the scrollSpeed option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({ scrollSpeed: 40 });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the scrollSpeed option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">scrollSpeed = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"scrollSpeed"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"scrollSpeed"</code><code class="plain">, 40 );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-tolerance" class="api-item">
<h3>tolerance<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>"intersect"</code>
</div>
<div>
				Specifies which mode to use for testing whether the item being moved is hovering over another item. Possible values:
				<ul>
					<li>
<code>"intersect"</code>: The item overlaps the other item by at least 50%.</li>
					<li>
<code>"pointer"</code>: The mouse pointer overlaps the other item.</li>
				</ul>
			</div>
<strong>Code examples:</strong><p>Initialize the sortable with the tolerance option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({ tolerance: </code><code class="string">"pointer"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the tolerance option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">tolerance = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"tolerance"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"tolerance"</code><code class="plain">, </code><code class="string">"pointer"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-zIndex" class="api-item">
<h3>zIndex<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Integer">Integer</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>1000</code>
</div>
<div>Z-index for element/helper while being sorted.</div>
<strong>Code examples:</strong><p>Initialize the sortable with the zIndex option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({ zIndex: 9999 });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the zIndex option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">zIndex = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"zIndex"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"zIndex"</code><code class="plain">, 9999 );</code></div></div></td></tr></tbody></table></div>
</div></section><section id="methods"><header><h2 class="underline">Methods</h2></header><div id="method-cancel"><div class="api-item first-item">
<h3>cancel()</h3>
<div>Cancels a change in the current sortable and reverts it to the state prior to when the current sort was started. Useful in the stop and receive callback functions.</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the cancel method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"cancel"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-destroy"><div class="api-item">
<h3>destroy()</h3>
<div>
		Removes the sortable functionality completely. This will return the element back to its pre-init state.
	</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the destroy method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"destroy"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-disable"><div class="api-item">
<h3>disable()</h3>
<div>
		Disables the sortable.
	</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the disable method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"disable"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-enable"><div class="api-item">
<h3>enable()</h3>
<div>
		Enables the sortable.
	</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the enable method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"enable"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-option">
<div class="api-item">
<h3>option( optionName )<span class="returns">Returns: <a href="http://api.jquery.com/Types#Object">Object</a></span>
</h3>
<div>Gets the value currently associated with the specified <code>optionName</code>.</div>
<ul><li>
<div><strong>optionName</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#String">String</a>
</div>
<div>The name of the option to get.</div>
</li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the  method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="keyword">var</code> <code class="plain">isDisabled = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"disabled"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div class="api-item">
<h3>option()<span class="returns">Returns: <a href="http://api.jquery.com/Types#PlainObject">PlainObject</a></span>
</h3>
<div>Gets an object containing key/value pairs representing the current sortable options hash.</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the  method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="keyword">var</code> <code class="plain">options = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div class="api-item">
<h3>option( optionName, value )</h3>
<div>Sets the value of the sortable option associated with the specified <code>optionName</code>.</div>
<ul>
<li>
<div><strong>optionName</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#String">String</a>
</div>
<div>The name of the option to set.</div>
</li>
<li>
<div><strong>value</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>A value to set for the option.</div>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Invoke the  method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"disabled"</code><code class="plain">, </code><code class="keyword">true</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div class="api-item">
<h3>option( options )</h3>
<div>Sets one or more options for the sortable.</div>
<ul><li>
<div><strong>options</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>A map of option-value pairs to set.</div>
</li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the  method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"option"</code><code class="plain">, { disabled: </code><code class="keyword">true</code> <code class="plain">} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
</div>
<div id="method-refresh"><div class="api-item">
<h3>refresh()</h3>
<div>Refresh the sortable items. Triggers the reloading of all sortable items, causing new items to be recognized.</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the refresh method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"refresh"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-refreshPositions"><div class="api-item">
<h3>refreshPositions()</h3>
<div>Refresh the cached positions of the sortable items. Calling this method refreshes the cached item positions of all sortables.</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the refreshPositions method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"refreshPositions"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-serialize"><div class="api-item">
<h3>serialize( options )<span class="returns">Returns: <a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div>
				<p>Serializes the sortable's item <code>id</code>s into a form/ajax submittable string. Calling this method produces a hash that can be appended to any url to easily submit a new item order back to the server.</p>

				<p>It works by default by looking at the <code>id</code> of each item in the format <code>"setname_number"</code>, and it spits out a hash like <code>"setname[]=number&amp;setname[]=number"</code>.</p>

				<p><em>Note: If serialize returns an empty string, make sure the <code>id</code> attributes include an underscore.  They must be in the form: <code>"set_number"</code> For example, a 3 element list with <code>id</code> attributes <code>"foo_1"</code>, <code>"foo_5"</code>, <code>"foo_2"</code> will serialize to <code>"foo[]=1&amp;foo[]=5&amp;foo[]=2"</code>. You can use an underscore, equal sign or hyphen to separate the set and number. For example <code>"foo=1"</code>, <code>"foo-1"</code>, and <code>"foo_1"</code> all serialize to <code>"foo[]=1"</code>.</em></p>
			</div>
<ul><li>
<div><strong>options</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>Options to customize the serialization.</div>
<ul>
<li>
<div>
<strong>key</strong> (default: <code>the part of the attribute in front of the separator</code>)</div>
<div>Type: <a href="http://api.jquery.com/Types#String">String</a>
</div>
<div>Replaces <code>part1[]</code> with the specified value.</div>
</li>
<li>
<div>
<strong>attribute</strong> (default: <code>"id"</code>)</div>
<div>Type: <a href="http://api.jquery.com/Types#String">String</a>
</div>
<div>The name of the attribute to use for the values.</div>
</li>
<li>
<div>
<strong>expression</strong> (default: <code>/(.+)[-=_](.+)/</code>)</div>
<div>Type: <a href="http://api.jquery.com/Types#RegExp">RegExp</a>
</div>
<div>A regular expression used to split the attribute value into key and value parts.</div>
</li>
</ul>
</li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the serialize method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="keyword">var</code> <code class="plain">sorted = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"serialize"</code><code class="plain">, { key: </code><code class="string">"sort"</code> <code class="plain">} );</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-toArray"><div class="api-item">
<h3>toArray()<span class="returns">Returns: <a href="http://api.jquery.com/Types#Array">Array</a></span>
</h3>
<div>Serializes the sortable's item id's into an array of string.</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the toArray method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="keyword">var</code> <code class="plain">sortedIDs = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"toArray"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-widget"><div class="api-item">
<h3>widget()<span class="returns">Returns: <a href="http://api.jquery.com/Types#jQuery">jQuery</a></span>
</h3>
<div>
		Returns a <code>jQuery</code> object containing the sortable element.
	</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the widget method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="keyword">var</code> <code class="plain">widget = $( </code><code class="string">".selector"</code> <code class="plain">).sortable( </code><code class="string">"widget"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div></section><section id="events"><header><h2 class="underline">Events</h2></header><div id="event-activate" class="api-item first-item">
<h3>activate( event, ui )<span class="returns">Type: <code>sortactivate</code></span>
</h3>
<div>This event is triggered when using connected lists, every connected list on drag start receives it.</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
<ul>
<li>
<div><strong>helper</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The jQuery object representing the helper being sorted</div>
</li>
<li>
<div><strong>item</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The jQuery object representing the current dragged element</div>
</li>
<li>
<div><strong>offset</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current absolute position of the helper represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>position</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current position of the helper represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>originalPosition</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The original position of the element represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>sender</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The sortable that the item comes from if moving from one sortable to another</div>
</li>
</ul>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the sortable with the activate callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">activate: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the sortactivate event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"sortactivate"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div id="event-beforeStop" class="api-item">
<h3>beforeStop( event, ui )<span class="returns">Type: <code>sortbeforestop</code></span>
</h3>
<div>This event is triggered when sorting stops, but when the placeholder/helper is still available.</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
<ul>
<li>
<div><strong>helper</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The jQuery object representing the helper being sorted</div>
</li>
<li>
<div><strong>item</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The jQuery object representing the current dragged element</div>
</li>
<li>
<div><strong>offset</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current absolute position of the helper represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>position</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current position of the helper represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>originalPosition</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The original position of the element represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>sender</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The sortable that the item comes from if moving from one sortable to another</div>
</li>
</ul>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the sortable with the beforeStop callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">beforeStop: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the sortbeforestop event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"sortbeforestop"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div id="event-change" class="api-item">
<h3>change( event, ui )<span class="returns">Type: <code>sortchange</code></span>
</h3>
<div>This event is triggered during sorting, but only when the DOM position has changed.</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
<ul>
<li>
<div><strong>helper</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The jQuery object representing the helper being sorted</div>
</li>
<li>
<div><strong>item</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The jQuery object representing the current dragged element</div>
</li>
<li>
<div><strong>offset</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current absolute position of the helper represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>position</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current position of the helper represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>originalPosition</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The original position of the element represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>sender</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The sortable that the item comes from if moving from one sortable to another</div>
</li>
</ul>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the sortable with the change callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">change: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the sortchange event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"sortchange"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div id="event-create" class="api-item">
<h3>create( event, ui )<span class="returns">Type: <code>sortcreate</code></span>
</h3>
<div>
		Triggered when the sortable is created.
	</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the sortable with the create callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">create: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the sortcreate event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"sortcreate"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div id="event-deactivate" class="api-item">
<h3>deactivate( event, ui )<span class="returns">Type: <code>sortdeactivate</code></span>
</h3>
<div>This event is triggered when sorting was stopped, is propagated to all possible connected lists.</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
<ul>
<li>
<div><strong>helper</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The jQuery object representing the helper being sorted</div>
</li>
<li>
<div><strong>item</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The jQuery object representing the current dragged element</div>
</li>
<li>
<div><strong>offset</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current absolute position of the helper represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>position</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current position of the helper represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>originalPosition</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The original position of the element represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>sender</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The sortable that the item comes from if moving from one sortable to another</div>
</li>
</ul>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the sortable with the deactivate callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">deactivate: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the sortdeactivate event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"sortdeactivate"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div id="event-out" class="api-item">
<h3>out( event, ui )<span class="returns">Type: <code>sortout</code></span>
</h3>
<div>This event is triggered when a sortable item is moved away from a connected list.</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
<ul>
<li>
<div><strong>helper</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The jQuery object representing the helper being sorted</div>
</li>
<li>
<div><strong>item</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The jQuery object representing the current dragged element</div>
</li>
<li>
<div><strong>offset</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current absolute position of the helper represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>position</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current position of the helper represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>originalPosition</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The original position of the element represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>sender</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The sortable that the item comes from if moving from one sortable to another</div>
</li>
</ul>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the sortable with the out callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">out: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the sortout event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"sortout"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div id="event-over" class="api-item">
<h3>over( event, ui )<span class="returns">Type: <code>sortover</code></span>
</h3>
<div>This event is triggered when a sortable item is moved into a connected list.</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
<ul>
<li>
<div><strong>helper</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The jQuery object representing the helper being sorted</div>
</li>
<li>
<div><strong>item</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The jQuery object representing the current dragged element</div>
</li>
<li>
<div><strong>offset</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current absolute position of the helper represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>position</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current position of the helper represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>originalPosition</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The original position of the element represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>sender</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The sortable that the item comes from if moving from one sortable to another</div>
</li>
</ul>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the sortable with the over callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">over: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the sortover event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"sortover"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div id="event-receive" class="api-item">
<h3>receive( event, ui )<span class="returns">Type: <code>sortreceive</code></span>
</h3>
<div>This event is triggered when a connected sortable list has received an item from another list.</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
<ul>
<li>
<div><strong>helper</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The jQuery object representing the helper being sorted</div>
</li>
<li>
<div><strong>item</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The jQuery object representing the current dragged element</div>
</li>
<li>
<div><strong>offset</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current absolute position of the helper represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>position</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current position of the helper represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>originalPosition</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The original position of the element represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>sender</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The sortable that the item comes from if moving from one sortable to another</div>
</li>
</ul>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the sortable with the receive callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">receive: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the sortreceive event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"sortreceive"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div id="event-remove" class="api-item">
<h3>remove( event, ui )<span class="returns">Type: <code>sortremove</code></span>
</h3>
<div>This event is triggered when a sortable item has been dragged out from the list and into another.</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
<ul>
<li>
<div><strong>helper</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The jQuery object representing the helper being sorted</div>
</li>
<li>
<div><strong>item</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The jQuery object representing the current dragged element</div>
</li>
<li>
<div><strong>offset</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current absolute position of the helper represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>position</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current position of the helper represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>originalPosition</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The original position of the element represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>sender</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The sortable that the item comes from if moving from one sortable to another</div>
</li>
</ul>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the sortable with the remove callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">remove: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the sortremove event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"sortremove"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div id="event-sort" class="api-item">
<h3>sort( event, ui )<span class="returns">Type: <code>sort</code></span>
</h3>
<div>This event is triggered during sorting.</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
<ul>
<li>
<div><strong>helper</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The jQuery object representing the helper being sorted</div>
</li>
<li>
<div><strong>item</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The jQuery object representing the current dragged element</div>
</li>
<li>
<div><strong>offset</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current absolute position of the helper represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>position</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current position of the helper represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>originalPosition</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The original position of the element represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>sender</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The sortable that the item comes from if moving from one sortable to another</div>
</li>
</ul>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the sortable with the sort callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">sort: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the sort event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"sort"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div id="event-start" class="api-item">
<h3>start( event, ui )<span class="returns">Type: <code>sortstart</code></span>
</h3>
<div>This event is triggered when sorting starts.</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
<ul>
<li>
<div><strong>helper</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The jQuery object representing the helper being sorted</div>
</li>
<li>
<div><strong>item</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The jQuery object representing the current dragged element</div>
</li>
<li>
<div><strong>offset</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current absolute position of the helper represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>position</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current position of the helper represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>originalPosition</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The original position of the element represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>sender</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The sortable that the item comes from if moving from one sortable to another</div>
</li>
</ul>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the sortable with the start callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">start: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the sortstart event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"sortstart"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div id="event-stop" class="api-item">
<h3>stop( event, ui )<span class="returns">Type: <code>sortstop</code></span>
</h3>
<div>This event is triggered when sorting has stopped.</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
<ul>
<li>
<div><strong>helper</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The jQuery object representing the helper being sorted</div>
</li>
<li>
<div><strong>item</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The jQuery object representing the current dragged element</div>
</li>
<li>
<div><strong>offset</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current absolute position of the helper represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>position</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current position of the helper represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>originalPosition</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The original position of the element represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>sender</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The sortable that the item comes from if moving from one sortable to another</div>
</li>
</ul>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the sortable with the stop callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">stop: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the sortstop event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"sortstop"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div id="event-update" class="api-item">
<h3>update( event, ui )<span class="returns">Type: <code>sortupdate</code></span>
</h3>
<div>This event is triggered when the user stopped sorting and the DOM position has changed.</div>
<ul>
<li>
<div><strong>event</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Event">Event</a>
</div>
<div></div>
</li>
<li>
<div><strong>ui</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div></div>
<ul>
<li>
<div><strong>helper</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The jQuery object representing the helper being sorted</div>
</li>
<li>
<div><strong>item</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The jQuery object representing the current dragged element</div>
</li>
<li>
<div><strong>offset</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current absolute position of the helper represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>position</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The current position of the helper represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>originalPosition</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>The original position of the element represented as <code>{ top, left }</code>
</div>
</li>
<li>
<div><strong>sender</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#jQuery">jQuery</a>
</div>
<div>The sortable that the item comes from if moving from one sortable to another</div>
</li>
</ul>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Initialize the sortable with the update callback specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).sortable({</code></div><div class="line number2 index1 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">update: </code><code class="keyword">function</code><code class="plain">( event, ui ) {}</code></div><div class="line number3 index2 alt2"><code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Bind an event listener to the sortupdate event:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).on( </code><code class="string">"sortupdate"</code><code class="plain">, </code><code class="keyword">function</code><code class="plain">( event, ui ) {} );</code></div></div></td></tr></tbody></table></div>
</div>
</div></section><section class="entry-examples" id="entry-examples"><header><h2 class="underline">Example:</h2></header><div class="entry-example" id="example-0">
<h4><span class="desc">A simple jQuery UI Sortable.</span></h4>
<div class="syntaxhighlighter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="gutter"><div class="line number1 index0 alt2">1</div><div class="line number2 index1 alt1">2</div><div class="line number3 index2 alt2">3</div><div class="line number4 index3 alt1">4</div><div class="line number5 index4 alt2">5</div><div class="line number6 index5 alt1">6</div><div class="line number7 index6 alt2">7</div><div class="line number8 index7 alt1">8</div><div class="line number9 index8 alt2">9</div><div class="line number10 index9 alt1">10</div><div class="line number11 index10 alt2">11</div><div class="line number12 index11 alt1">12</div><div class="line number13 index12 alt2">13</div><div class="line number14 index13 alt1">14</div><div class="line number15 index14 alt2">15</div><div class="line number16 index15 alt1">16</div><div class="line number17 index16 alt2">17</div><div class="line number18 index17 alt1">18</div><div class="line number19 index18 alt2">19</div><div class="line number20 index19 alt1">20</div><div class="line number21 index20 alt2">21</div><div class="line number22 index21 alt1">22</div><div class="line number23 index22 alt2">23</div></td><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">&lt;!doctype html&gt;</code></div><div class="line number2 index1 alt1"><code class="plain">&lt;</code><code class="keyword">html</code> <code class="color1">lang</code><code class="plain">=</code><code class="string">"en"</code><code class="plain">&gt;</code></div><div class="line number3 index2 alt2"><code class="plain">&lt;</code><code class="keyword">head</code><code class="plain">&gt;</code></div><div class="line number4 index3 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">meta</code> <code class="color1">charset</code><code class="plain">=</code><code class="string">"utf-8"</code><code class="plain">&gt;</code></div><div class="line number5 index4 alt2"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">title</code><code class="plain">&gt;sortable demo&lt;/</code><code class="keyword">title</code><code class="plain">&gt;</code></div><div class="line number6 index5 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">link</code> <code class="color1">rel</code><code class="plain">=</code><code class="string">"stylesheet"</code> <code class="color1">href</code><code class="plain">=</code><code class="string">"<a href="http://code.jquery.com/ui/1.9.2/themes/base/jquery-ui.css">http://code.jquery.com/ui/1.9.2/themes/base/jquery-ui.css</a>"</code><code class="plain">&gt;</code></div><div class="line number7 index6 alt2"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">script</code> <code class="color1">src</code><code class="plain">=</code><code class="string">"<a href="http://code.jquery.com/jquery-1.8.3.js">http://code.jquery.com/jquery-1.8.3.js</a>"</code><code class="plain">&gt;&lt;/</code><code class="keyword">script</code><code class="plain">&gt;</code></div><div class="line number8 index7 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">script</code> <code class="color1">src</code><code class="plain">=</code><code class="string">"<a href="http://code.jquery.com/ui/1.9.2/jquery-ui.js">http://code.jquery.com/ui/1.9.2/jquery-ui.js</a>"</code><code class="plain">&gt;&lt;/</code><code class="keyword">script</code><code class="plain">&gt;</code></div><div class="line number9 index8 alt2"><code class="plain">&lt;/</code><code class="keyword">head</code><code class="plain">&gt;</code></div><div class="line number10 index9 alt1"><code class="plain">&lt;</code><code class="keyword">body</code><code class="plain">&gt;</code></div><div class="line number11 index10 alt2">&nbsp;</div><div class="line number12 index11 alt1"><code class="plain">&lt;</code><code class="keyword">ul</code> <code class="color1">id</code><code class="plain">=</code><code class="string">"sortable"</code><code class="plain">&gt;</code></div><div class="line number13 index12 alt2"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">li</code><code class="plain">&gt;Item 1&lt;/</code><code class="keyword">li</code><code class="plain">&gt;</code></div><div class="line number14 index13 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">li</code><code class="plain">&gt;Item 2&lt;/</code><code class="keyword">li</code><code class="plain">&gt;</code></div><div class="line number15 index14 alt2"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">li</code><code class="plain">&gt;Item 3&lt;/</code><code class="keyword">li</code><code class="plain">&gt;</code></div><div class="line number16 index15 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">li</code><code class="plain">&gt;Item 4&lt;/</code><code class="keyword">li</code><code class="plain">&gt;</code></div><div class="line number17 index16 alt2"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">li</code><code class="plain">&gt;Item 5&lt;/</code><code class="keyword">li</code><code class="plain">&gt;</code></div><div class="line number18 index17 alt1"><code class="plain">&lt;/</code><code class="keyword">ul</code><code class="plain">&gt;</code></div><div class="line number19 index18 alt2">&nbsp;</div><div class="line number20 index19 alt1"><code class="plain">&lt;</code><code class="keyword">script</code><code class="plain">&gt;$("#sortable").sortable();&lt;/</code><code class="keyword">script</code><code class="plain">&gt;</code></div><div class="line number21 index20 alt2">&nbsp;</div><div class="line number22 index21 alt1"><code class="plain">&lt;/</code><code class="keyword">body</code><code class="plain">&gt;</code></div><div class="line number23 index22 alt2"><code class="plain">&lt;/</code><code class="keyword">html</code><code class="plain">&gt;</code></div></div></td></tr></tbody></table></div>
<h4>Demo:</h4>
<div class="demo code-demo" data-height="150"></div>
</div></section>
</div></article>

</body>
</html>
