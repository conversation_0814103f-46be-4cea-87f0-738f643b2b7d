using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DebtDesk.Core.Entities
{
    /// <summary>
    /// Represents review/audit cases
    /// </summary>
    public class Review : BaseEntity
    {
        [Required]
        [MaxLength(200)]
        public string FileName { get; set; } = string.Empty;

        public DateTime Time { get; set; }

        public ReviewType? Type { get; set; }

        [MaxLength(200)]
        public string? FinalFile { get; set; }

        public int Status { get; set; } = 0;

        // Foreign key
        public int FileId { get; set; }

        // Navigation properties
        [ForeignKey("FileId")]
        public virtual File File { get; set; } = null!;

        public virtual ICollection<ReviewDocument> Documents { get; set; } = new List<ReviewDocument>();
    }

    public enum ReviewType
    {
        SOFT,
        FULL,
        FAST_FULL,
        COMPLEX
    }
}
