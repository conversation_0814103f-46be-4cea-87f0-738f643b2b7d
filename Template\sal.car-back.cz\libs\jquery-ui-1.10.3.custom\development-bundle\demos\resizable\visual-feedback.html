<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>jQuery UI Resizable - Visual feedback</title>
	<link rel="stylesheet" href="../../themes/base/jquery.ui.all.css">
	<script src="../../jquery-1.9.1.js"></script>
	<script src="../../ui/jquery.ui.core.js"></script>
	<script src="../../ui/jquery.ui.widget.js"></script>
	<script src="../../ui/jquery.ui.mouse.js"></script>
	<script src="../../ui/jquery.ui.resizable.js"></script>
	<link rel="stylesheet" href="../demos.css">
	<style>
	#resizable { width: 150px; height: 150px; padding: 0.5em; }
	#resizable h3 { text-align: center; margin: 0; }
	.ui-resizable-ghost { border: 1px dotted gray; }
	</style>
	<script>
	$(function() {
		$( "#resizable" ).resizable({
			ghost: true
		});
	});
	</script>
</head>
<body>

<div id="resizable" class="ui-widget-content">
	<h3 class="ui-widget-header">Ghost</h3>
</div>

<div class="demo-description">
<p>Instead of showing the actual element during resize, set the <code>ghost</code> option to true to show a semi-transparent part of the element.</p>
</div>
</body>
</html>
