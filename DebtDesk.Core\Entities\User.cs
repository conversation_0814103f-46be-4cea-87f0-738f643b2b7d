using System.ComponentModel.DataAnnotations;

namespace DebtDesk.Core.Entities
{
    public class User : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string Username { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string Password { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string Mail { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string Role { get; set; } = string.Empty;

        [MaxLength(10)]
        public string Color { get; set; } = "white";

        [Required]
        [MaxLength(100)]
        public string OrigMail { get; set; } = string.Empty;

        // Navigation properties
        public virtual ICollection<DebtCase> DebtCases { get; set; } = new List<DebtCase>();
    }
}
