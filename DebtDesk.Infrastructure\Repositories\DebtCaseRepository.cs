using Microsoft.EntityFrameworkCore;
using DebtDesk.Core.Entities;
using DebtDesk.Core.Enums;
using DebtDesk.Core.Interfaces;
using DebtDesk.Infrastructure.Data;

namespace DebtDesk.Infrastructure.Repositories
{
    public class DebtCaseRepository : Repository<DebtCase>, IDebtCaseRepository
    {
        public DebtCaseRepository(DebtDeskDbContext context) : base(context)
        {
        }

        public async Task<IEnumerable<DebtCase>> GetNewDebtCasesAsync()
        {
            return await _dbSet
                .Include(d => d.File)
                .Where(d => d.Stav == (byte)DebtCaseStatus.Mail)
                .Where(d => d.File.Name.Contains("EXP_VExtAg_") || d.File.Name.Contains("EXP_VExtAgMIM_"))
                .OrderByDescending(d => d.DatumExportuNaAg)
                .ToListAsync();
        }

        public async Task<IEnumerable<DebtCase>> GetNewRUCasesAsync()
        {
            return await _dbSet
                .Include(d => d.File)
                .Where(d => d.Stav == (byte)DebtCaseStatus.Mail)
                .Where(d => d.File.Name.Contains("EXP_VExtAgZbPohlRU_"))
                .OrderByDescending(d => d.DatumExportuNaAg)
                .ToListAsync();
        }

        public async Task<IEnumerable<DebtCase>> GetNewKVCasesAsync()
        {
            return await _dbSet
                .Include(d => d.File)
                .Where(d => d.Stav == (byte)DebtCaseStatus.Mail)
                .Where(d => d.File.Name.Contains("EXP_VExtAgZbPohl_"))
                .OrderByDescending(d => d.DatumExportuNaAg)
                .ToListAsync();
        }

        public async Task<IEnumerable<DebtCase>> GetImportedCasesAsync()
        {
            return await _dbSet
                .Include(d => d.File)
                .Where(d => d.Stav == (byte)DebtCaseStatus.Import)
                .OrderByDescending(d => d.DatumExportuNaAg)
                .ToListAsync();
        }

        public async Task<IEnumerable<DebtCase>> GetCasesForDivisionAsync()
        {
            return await _dbSet
                .Include(d => d.File)
                .Where(d => d.Stav == (byte)DebtCaseStatus.Division)
                .OrderBy(d => d.Id)
                .ToListAsync();
        }

        public async Task<IEnumerable<DebtCase>> GetOpenCasesAsync()
        {
            return await _dbSet
                .Include(d => d.File)
                .Include(d => d.User)
                .Include(d => d.DebtType)
                .Where(d => d.Stav == (byte)DebtCaseStatus.Open)
                .ToListAsync();
        }

        public async Task<IEnumerable<DebtCase>> GetOpenCasesByUserAsync(int userId)
        {
            return await _dbSet
                .Include(d => d.File)
                .Include(d => d.User)
                .Include(d => d.DebtType)
                .Where(d => d.Stav == (byte)DebtCaseStatus.Open && d.UserId == userId)
                .ToListAsync();
        }

        public async Task<IEnumerable<DebtCase>> GetClosedCasesAsync()
        {
            return await _dbSet
                .Include(d => d.File)
                .Include(d => d.User)
                .Include(d => d.DebtType)
                .Where(d => d.Stav == (byte)DebtCaseStatus.Close)
                .ToListAsync();
        }

        public async Task<IEnumerable<DebtCase>> GetCasesByStatusAsync(DebtCaseStatus status)
        {
            return await _dbSet
                .Include(d => d.File)
                .Include(d => d.User)
                .Include(d => d.DebtType)
                .Where(d => d.Stav == (byte)status)
                .ToListAsync();
        }

        public async Task UpdateDebtCaseAsync(int id, Dictionary<string, object> updates)
        {
            var debtCase = await _dbSet.FindAsync(id);
            if (debtCase == null) return;

            var entry = _context.Entry(debtCase);
            foreach (var update in updates)
            {
                if (entry.Property(update.Key).Metadata.ClrType == typeof(DateTime) || 
                    entry.Property(update.Key).Metadata.ClrType == typeof(DateTime?))
                {
                    if (update.Value is string dateString && !string.IsNullOrEmpty(dateString))
                    {
                        if (DateTime.TryParse(dateString, out var dateValue))
                        {
                            entry.Property(update.Key).CurrentValue = dateValue;
                        }
                    }
                    else if (update.Value is DateTime dateTime)
                    {
                        entry.Property(update.Key).CurrentValue = dateTime;
                    }
                }
                else
                {
                    entry.Property(update.Key).CurrentValue = update.Value ?? DBNull.Value;
                }
            }
        }

        public async Task<DebtCase?> GetCaseByEvCisloAsync(string evCislo)
        {
            return await _dbSet
                .Include(d => d.File)
                .Include(d => d.User)
                .Include(d => d.DebtType)
                .FirstOrDefaultAsync(d => d.DluEvCislo == evCislo);
        }
    }
}
