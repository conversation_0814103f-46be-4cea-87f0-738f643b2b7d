<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>jQuery UI blind-effect documentation</title>

	<style>
	body {
		font-family: "Trebuchet MS", "Arial", "Helvetica", "Verdana", "sans-serif"
	}
	.gutter {
		display: none;
	}
	</style>
</head>
<body>

<script>{
		"title":
			"Blind Effect",
		"excerpt":
			"\n\t\tThe blind effect hides or shows an element by wrapping the element in a container, and \"pulling the blinds\"\n\t",
		"termSlugs": {
			"category": [
				"effects"
			]
		}
	}</script><article id="blind1" class="entry effect"><h2 class="section-title"><span>Blind Effect</span></h2>
<div class="entry-wrapper">
<p class="desc"><strong>Description: </strong>
		The blind effect hides or shows an element by wrapping the element in a container, and "pulling the blinds"
	</p>
<ul class="signatures"><li class="signature">
<h4 class="name">blind</h4>
<ul><li>
<div>
<strong>direction</strong> (default: <code>"up"</code>)</div>
<div>Type: <a href="http://api.jquery.com/Types#String">String</a>
</div>
<div>
				<p>The direction the blind will be pulled to hide the element, or the direction from which the element will be revealed.</p>
				<p>Possible Values: <code>up</code>, <code>down</code>, <code>left</code>, <code>right</code>, <code>vertical</code>, <code>horizontal</code>.</p>
			</div>
</li></ul>
</li></ul>
<div class="longdesc" id="entry-longdesc">
		<p>The container has <code>overflow: hidden</code> applied, so height changes affect what's visible.</p>
	</div>
<section class="entry-examples" id="entry-examples"><header><h2 class="underline">Example:</h2></header><div class="entry-example" id="example-0">
<h4><span class="desc">Toggle a div using the blind effect.</span></h4>
<div class="syntaxhighlighter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="gutter"><div class="line number1 index0 alt2">1</div><div class="line number2 index1 alt1">2</div><div class="line number3 index2 alt2">3</div><div class="line number4 index3 alt1">4</div><div class="line number5 index4 alt2">5</div><div class="line number6 index5 alt1">6</div><div class="line number7 index6 alt2">7</div><div class="line number8 index7 alt1">8</div><div class="line number9 index8 alt2">9</div><div class="line number10 index9 alt1">10</div><div class="line number11 index10 alt2">11</div><div class="line number12 index11 alt1">12</div><div class="line number13 index12 alt2">13</div><div class="line number14 index13 alt1">14</div><div class="line number15 index14 alt2">15</div><div class="line number16 index15 alt1">16</div><div class="line number17 index16 alt2">17</div><div class="line number18 index17 alt1">18</div><div class="line number19 index18 alt2">19</div><div class="line number20 index19 alt1">20</div><div class="line number21 index20 alt2">21</div><div class="line number22 index21 alt1">22</div><div class="line number23 index22 alt2">23</div><div class="line number24 index23 alt1">24</div><div class="line number25 index24 alt2">25</div><div class="line number26 index25 alt1">26</div><div class="line number27 index26 alt2">27</div><div class="line number28 index27 alt1">28</div><div class="line number29 index28 alt2">29</div></td><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">&lt;!doctype html&gt;</code></div><div class="line number2 index1 alt1"><code class="plain">&lt;</code><code class="keyword">html</code> <code class="color1">lang</code><code class="plain">=</code><code class="string">"en"</code><code class="plain">&gt;</code></div><div class="line number3 index2 alt2"><code class="plain">&lt;</code><code class="keyword">head</code><code class="plain">&gt;</code></div><div class="line number4 index3 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">meta</code> <code class="color1">charset</code><code class="plain">=</code><code class="string">"utf-8"</code><code class="plain">&gt;</code></div><div class="line number5 index4 alt2"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">title</code><code class="plain">&gt;blind demo&lt;/</code><code class="keyword">title</code><code class="plain">&gt;</code></div><div class="line number6 index5 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">link</code> <code class="color1">rel</code><code class="plain">=</code><code class="string">"stylesheet"</code> <code class="color1">href</code><code class="plain">=</code><code class="string">"<a href="http://code.jquery.com/ui/1.9.2/themes/base/jquery-ui.css">http://code.jquery.com/ui/1.9.2/themes/base/jquery-ui.css</a>"</code><code class="plain">&gt;</code></div><div class="line number7 index6 alt2"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">style</code><code class="plain">&gt;</code></div><div class="line number8 index7 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">#toggle {</code></div><div class="line number9 index8 alt2"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">width: 100px;</code></div><div class="line number10 index9 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">height: 100px;</code></div><div class="line number11 index10 alt2"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">background: #ccc;</code></div><div class="line number12 index11 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">}</code></div><div class="line number13 index12 alt2"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;/</code><code class="keyword">style</code><code class="plain">&gt;</code></div><div class="line number14 index13 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">script</code> <code class="color1">src</code><code class="plain">=</code><code class="string">"<a href="http://code.jquery.com/jquery-1.8.3.js">http://code.jquery.com/jquery-1.8.3.js</a>"</code><code class="plain">&gt;&lt;/</code><code class="keyword">script</code><code class="plain">&gt;</code></div><div class="line number15 index14 alt2"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">script</code> <code class="color1">src</code><code class="plain">=</code><code class="string">"<a href="http://code.jquery.com/ui/1.9.2/jquery-ui.js">http://code.jquery.com/ui/1.9.2/jquery-ui.js</a>"</code><code class="plain">&gt;&lt;/</code><code class="keyword">script</code><code class="plain">&gt;</code></div><div class="line number16 index15 alt1"><code class="plain">&lt;/</code><code class="keyword">head</code><code class="plain">&gt;</code></div><div class="line number17 index16 alt2"><code class="plain">&lt;</code><code class="keyword">body</code><code class="plain">&gt;</code></div><div class="line number18 index17 alt1">&nbsp;</div><div class="line number19 index18 alt2"><code class="plain">&lt;</code><code class="keyword">p</code><code class="plain">&gt;Click anywhere to toggle the box.&lt;/</code><code class="keyword">p</code><code class="plain">&gt;</code></div><div class="line number20 index19 alt1"><code class="plain">&lt;</code><code class="keyword">div</code> <code class="color1">id</code><code class="plain">=</code><code class="string">"toggle"</code><code class="plain">&gt;&lt;/</code><code class="keyword">div</code><code class="plain">&gt;</code></div><div class="line number21 index20 alt2">&nbsp;</div><div class="line number22 index21 alt1"><code class="plain">&lt;</code><code class="keyword">script</code><code class="plain">&gt;</code></div><div class="line number23 index22 alt2"><code class="plain">$( document ).click(function() {</code></div><div class="line number24 index23 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">$( "#toggle" ).toggle( "blind" );</code></div><div class="line number25 index24 alt2"><code class="plain">});</code></div><div class="line number26 index25 alt1"><code class="plain">&lt;/</code><code class="keyword">script</code><code class="plain">&gt;</code></div><div class="line number27 index26 alt2">&nbsp;</div><div class="line number28 index27 alt1"><code class="plain">&lt;/</code><code class="keyword">body</code><code class="plain">&gt;</code></div><div class="line number29 index28 alt2"><code class="plain">&lt;/</code><code class="keyword">html</code><code class="plain">&gt;</code></div></div></td></tr></tbody></table></div>
<h4>Demo:</h4>
<div class="demo code-demo" data-height="200"></div>
</div></section>
</div></article>

</body>
</html>
