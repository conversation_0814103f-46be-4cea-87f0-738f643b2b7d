<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>jQuery UI Progressbar - Indeterminate Value</title>
	<link rel="stylesheet" href="../../themes/base/jquery.ui.all.css">
	<script src="../../jquery-1.9.1.js"></script>
	<script src="../../ui/jquery.ui.core.js"></script>
	<script src="../../ui/jquery.ui.widget.js"></script>
	<script src="../../ui/jquery.ui.progressbar.js"></script>
	<link rel="stylesheet" href="../demos.css">
	<script>
	$(function() {
		$( "#progressbar" ).progressbar({
			value: false
		});
		$( "button" ).on( "click", function( event ) {
			var target = $( event.target ),
				progressbar = $( "#progressbar" ),
				progressbarValue = progressbar.find( ".ui-progressbar-value" );

			if ( target.is( "#numButton" ) ) {
				progressbar.progressbar( "option", {
					value: Math.floor( Math.random() * 100 )
				});
			} else if ( target.is( "#colorButton" ) ) {
				progressbarValue.css({
					"background": '#' + Math.floor( Math.random() * 16777215 ).toString( 16 )
				});
			} else if ( target.is( "#falseButton" ) ) {
				progressbar.progressbar( "option", "value", false );
			}
		});
	});
	</script>
	<style>
	#progressbar .ui-progressbar-value {
		background-color: #ccc;
	}
	</style>
</head>
<body>

<div id="progressbar"></div>
<button id="numButton">Random Value - Determinate</button>
<button id="falseButton">Indeterminate</button>
<button id="colorButton">Random Color</button>

<div class="demo-description">
<p>Indeterminate progress bar and switching between determinate and indeterminate styles.</p>
</div>
</body>
</html>
