using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using DebtDesk.Core.Interfaces;
using DebtDesk.Core.Enums;
using DebtDesk.Web.Models;

namespace DebtDesk.Web.Controllers
{
    [Authorize]
    public class DebtCasesController : Controller
    {
        private readonly IUnitOfWork _unitOfWork;

        public DebtCasesController(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        // GET: DebtCases
        public async Task<IActionResult> Index()
        {
            var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");

            IEnumerable<Core.Entities.DebtCase> debtCases;

            if (userRole == UserRole.ADMIN)
            {
                debtCases = await _unitOfWork.DebtCases.GetOpenCasesAsync();
            }
            else
            {
                debtCases = await _unitOfWork.DebtCases.GetOpenCasesByUserAsync(userId);
            }

            var viewModel = debtCases.Select(dc => new DebtCaseViewModel
            {
                Id = dc.Id,
                DluEvCislo = dc.DluEvCislo,
                PartnerNazev = dc.PartnerNazev,
                DatumExportuNaAg = dc.DatumExportuNaAg,
                DatumUkonceniVymahani = dc.DatumUkonceniVymahani,
                Stav = (DebtCaseStatus)dc.Stav,
                UserName = dc.User?.Name,
                DebtTypeName = dc.DebtType?.Text
            }).ToList();

            return View(viewModel);
        }

        // GET: DebtCases/New
        [Authorize(Roles = UserRole.NEWDLU)]
        public async Task<IActionResult> New()
        {
            var newCases = await _unitOfWork.DebtCases.GetNewDebtCasesAsync();
            
            var viewModel = newCases.Select(dc => new DebtCaseViewModel
            {
                Id = dc.Id,
                DluEvCislo = dc.DluEvCislo,
                PartnerNazev = dc.PartnerNazev,
                DatumExportuNaAg = dc.DatumExportuNaAg,
                Stav = (DebtCaseStatus)dc.Stav,
                FileName = dc.File.Name
            }).ToList();

            return View(viewModel);
        }

        // GET: DebtCases/RU
        [Authorize(Roles = UserRole.DLU)]
        public async Task<IActionResult> RU()
        {
            var ruCases = await _unitOfWork.DebtCases.GetNewRUCasesAsync();
            
            var viewModel = ruCases.Select(dc => new DebtCaseViewModel
            {
                Id = dc.Id,
                DluEvCislo = dc.DluEvCislo,
                PartnerNazev = dc.PartnerNazev,
                DatumExportuNaAg = dc.DatumExportuNaAg,
                Stav = (DebtCaseStatus)dc.Stav,
                FileName = dc.File.Name
            }).ToList();

            return View(viewModel);
        }

        // GET: DebtCases/KV
        [Authorize(Roles = UserRole.DLU)]
        public async Task<IActionResult> KV()
        {
            var kvCases = await _unitOfWork.DebtCases.GetNewKVCasesAsync();
            
            var viewModel = kvCases.Select(dc => new DebtCaseViewModel
            {
                Id = dc.Id,
                DluEvCislo = dc.DluEvCislo,
                PartnerNazev = dc.PartnerNazev,
                DatumExportuNaAg = dc.DatumExportuNaAg,
                Stav = (DebtCaseStatus)dc.Stav,
                KvStav = dc.KvStav,
                FileName = dc.File.Name
            }).ToList();

            return View(viewModel);
        }

        // GET: DebtCases/Closed
        [Authorize(Roles = UserRole.DLU)]
        public async Task<IActionResult> Closed()
        {
            var closedCases = await _unitOfWork.DebtCases.GetClosedCasesAsync();
            
            var viewModel = closedCases.Select(dc => new DebtCaseViewModel
            {
                Id = dc.Id,
                DluEvCislo = dc.DluEvCislo,
                PartnerNazev = dc.PartnerNazev,
                DatumExportuNaAg = dc.DatumExportuNaAg,
                DatumUkonceniVymahani = dc.DatumUkonceniVymahani,
                Stav = (DebtCaseStatus)dc.Stav,
                UserName = dc.User?.Name
            }).ToList();

            return View(viewModel);
        }

        // GET: DebtCases/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var debtCase = await _unitOfWork.DebtCases.GetByIdAsync(id);
            if (debtCase == null)
            {
                return NotFound();
            }

            var paymentSchedules = await _unitOfWork.PaymentSchedules.GetPaymentSchedulesByDebtCaseAsync(debtCase.DluEvCislo ?? "");
            var payments = await _unitOfWork.Payments.FindAsync(p => p.DluEvCislo == debtCase.DluEvCislo);

            var viewModel = new DebtCaseDetailsViewModel
            {
                DebtCase = new DebtCaseViewModel
                {
                    Id = debtCase.Id,
                    DluEvCislo = debtCase.DluEvCislo,
                    PartnerNazev = debtCase.PartnerNazev,
                    PartnerRc = debtCase.PartnerRc,
                    PartnerIc = debtCase.PartnerIc,
                    PartnerEmail = debtCase.PartnerEmail,
                    PartnerMobil = debtCase.PartnerMobil,
                    PartnerUlicePref = debtCase.PartnerUlicePref,
                    PartnerMestoPref = debtCase.PartnerMestoPref,
                    PartnerPscPref = debtCase.PartnerPscPref,
                    DatumExportuNaAg = debtCase.DatumExportuNaAg,
                    DatumUkonceniVymahani = debtCase.DatumUkonceniVymahani,
                    Stav = (DebtCaseStatus)debtCase.Stav,
                    UserName = debtCase.User?.Name,
                    DebtTypeName = debtCase.DebtType?.Text,
                    VozidloSpz = debtCase.VozidloSpz,
                    VozidloZnacka = debtCase.VozidloZnacka,
                    VozidloModel = debtCase.VozidloModel,
                    VozidloRokVyroby = debtCase.VozidloRokVyroby
                },
                PaymentSchedules = paymentSchedules.Select(ps => new PaymentScheduleViewModel
                {
                    Id = ps.Id,
                    PopisSplatky = ps.PopisSplatky,
                    DatumSplatnostiSplatky = ps.DatumSplatnostiSplatky,
                    VyseUhradySplatky = ps.VyseUhradySplatky,
                    VyseZustatku = ps.VyseZustatku,
                    TypSplatky = ps.TypSplatky
                }).ToList(),
                Payments = payments.Select(p => new PaymentViewModel
                {
                    Id = p.Id,
                    DatumUhrady = p.DatumUhrady,
                    CastkaUhradyMenaSmlouvy = p.CastkaUhradyMenaSmlouvy,
                    Status = p.Status
                }).ToList()
            };

            return View(viewModel);
        }

        // GET: DebtCases/Division
        [Authorize(Roles = UserRole.NEWDLU)]
        public async Task<IActionResult> Division()
        {
            var casesForDivision = await _unitOfWork.DebtCases.GetCasesForDivisionAsync();
            
            var viewModel = casesForDivision.Select(dc => new DebtCaseViewModel
            {
                Id = dc.Id,
                DluEvCislo = dc.DluEvCislo,
                PartnerNazev = dc.PartnerNazev,
                DatumExportuNaAg = dc.DatumExportuNaAg,
                Stav = (DebtCaseStatus)dc.Stav
            }).ToList();

            return View(viewModel);
        }
    }
}
