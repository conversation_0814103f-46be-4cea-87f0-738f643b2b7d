﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - DebtDesk.Web</title>
    <script type="importmap"></script>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/DebtDesk.Web.styles.css" asp-append-version="true" />
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-sm navbar-toggleable-sm navbar-dark bg-primary border-bottom box-shadow mb-3">
            <div class="container-fluid">
                <a class="navbar-brand" asp-area="" asp-controller="Home" asp-action="Index">DebtDesk</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-sm-inline-flex justify-content-between">
                    @if (User.Identity?.IsAuthenticated == true)
                    {
                        <ul class="navbar-nav flex-grow-1">
                            @if (User.IsInRole("NEWDLU"))
                            {
                                <li class="nav-item">
                                    <a class="nav-link text-white" asp-controller="DebtCases" asp-action="New">Nové případy</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link text-white" asp-controller="DebtCases" asp-action="Division">Rozdělení případů</a>
                                </li>
                            }
                            @if (User.IsInRole("DLU"))
                            {
                                <li class="nav-item">
                                    <a class="nav-link text-white" asp-controller="DebtCases" asp-action="Index">Seznam případů</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link text-white" asp-controller="DebtCases" asp-action="RU">RU případy</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link text-white" asp-controller="DebtCases" asp-action="KV">KV případy</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link text-white" asp-controller="DebtCases" asp-action="Closed">Uzavřené případy</a>
                                </li>
                            }
                        </ul>
                        <ul class="navbar-nav">
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle text-white" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    @(User.FindFirst("FullName")?.Value ?? User.Identity.Name)
                                </a>
                                <ul class="dropdown-menu" aria-labelledby="navbarDropdown">
                                    <li>
                                        <form asp-controller="Account" asp-action="Logout" method="post" class="d-inline">
                                            <button type="submit" class="dropdown-item">Logout</button>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        </ul>
                    }
                </div>
            </div>
        </nav>
    </header>
    <div class="container">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="border-top footer text-muted">
        <div class="container">
            &copy; 2025 - DebtDesk.Web - <a asp-area="" asp-controller="Home" asp-action="Privacy">Privacy</a>
        </div>
    </footer>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
