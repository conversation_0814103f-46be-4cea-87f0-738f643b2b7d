using Microsoft.EntityFrameworkCore.Storage;
using DebtDesk.Core.Entities;
using DebtDesk.Core.Interfaces;
using DebtDesk.Infrastructure.Data;

namespace DebtDesk.Infrastructure.Repositories
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly DebtDeskDbContext _context;
        private IDbContextTransaction? _transaction;

        public UnitOfWork(DebtDeskDbContext context)
        {
            _context = context;
            
            DebtCases = new DebtCaseRepository(_context);
            PaymentSchedules = new PaymentScheduleRepository(_context);
            Payments = new Repository<Payment>(_context);
            Users = new Repository<User>(_context);
            Files = new Repository<Core.Entities.File>(_context);
            DebtTypes = new Repository<DebtType>(_context);
            Photos = new Repository<Photo>(_context);
            Reviews = new Repository<Review>(_context);
            ReviewDocuments = new Repository<ReviewDocument>(_context);
            DebtCaseDocuments = new Repository<DebtCaseDocument>(_context);
        }

        public IDebtCaseRepository DebtCases { get; }
        public IPaymentScheduleRepository PaymentSchedules { get; }
        public IRepository<Payment> Payments { get; }
        public IRepository<User> Users { get; }
        public IRepository<Core.Entities.File> Files { get; }
        public IRepository<DebtType> DebtTypes { get; }
        public IRepository<Photo> Photos { get; }
        public IRepository<Review> Reviews { get; }
        public IRepository<ReviewDocument> ReviewDocuments { get; }
        public IRepository<DebtCaseDocument> DebtCaseDocuments { get; }

        public async Task<int> SaveChangesAsync()
        {
            return await _context.SaveChangesAsync();
        }

        public async Task BeginTransactionAsync()
        {
            _transaction = await _context.Database.BeginTransactionAsync();
        }

        public async Task CommitTransactionAsync()
        {
            if (_transaction != null)
            {
                await _transaction.CommitAsync();
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        public async Task RollbackTransactionAsync()
        {
            if (_transaction != null)
            {
                await _transaction.RollbackAsync();
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        public void Dispose()
        {
            _transaction?.Dispose();
            _context.Dispose();
        }
    }
}
