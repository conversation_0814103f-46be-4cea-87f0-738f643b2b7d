using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DebtDesk.Core.Entities
{
    /// <summary>
    /// Represents photos related to debt cases
    /// </summary>
    public class Photo : BaseEntity
    {
        [Required]
        [MaxLength(50)]
        public string FileName { get; set; } = string.Empty;

        [Required]
        [MaxLength(50)]
        public string Thumb { get; set; } = string.Empty;

        [MaxLength(50)]
        public string? Text { get; set; }

        public DateTime? Upload { get; set; }

        public DateTime? Sent { get; set; }

        // Foreign key
        public int DebtCaseId { get; set; }

        // Navigation properties
        [ForeignKey("DebtCaseId")]
        public virtual DebtCase DebtCase { get; set; } = null!;
    }
}
