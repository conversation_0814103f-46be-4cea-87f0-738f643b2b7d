using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DebtDesk.Core.Entities
{
    /// <summary>
    /// Represents documents related to debt cases (dludocs)
    /// </summary>
    public class DebtCaseDocument : BaseEntity
    {
        [Required]
        [MaxLength(200)]
        public string FileName { get; set; } = string.Empty;

        public int FileType { get; set; } = 0;

        public DateTime? Send { get; set; }

        public DateTime? MailSend { get; set; }

        // Foreign key
        public int DebtCaseId { get; set; }

        // Navigation properties
        [ForeignKey("DebtCaseId")]
        public virtual DebtCase DebtCase { get; set; } = null!;
    }
}
