using Microsoft.EntityFrameworkCore;
using System.Security.Cryptography;
using System.Text;
using DebtDesk.Core.Entities;
using DebtDesk.Core.Services;
using DebtDesk.Infrastructure.Data;

namespace DebtDesk.Infrastructure.Services
{
    public class AuthenticationService : IAuthenticationService
    {
        private readonly DebtDeskDbContext _context;

        public AuthenticationService(DebtDeskDbContext context)
        {
            _context = context;
        }

        public async Task<User?> AuthenticateAsync(string username, string password)
        {
            var user = await _context.Users
                .FirstOrDefaultAsync(u => u.Username == username);

            if (user == null)
                return null;

            // For now, we'll do simple password comparison
            // In production, you should use proper password hashing
            if (user.Password == password)
                return user;

            return null;
        }

        public async Task<bool> ValidateUserAsync(string username, string password)
        {
            var user = await AuthenticateAsync(username, password);
            return user != null;
        }

        public string HashPassword(string password)
        {
            // Simple MD5 hash for compatibility with existing system
            // In production, use BCrypt or similar
            using var md5 = MD5.Create();
            var inputBytes = Encoding.UTF8.GetBytes(password);
            var hashBytes = md5.ComputeHash(inputBytes);
            return Convert.ToHexString(hashBytes).ToLower();
        }

        public bool VerifyPassword(string password, string hashedPassword)
        {
            var hashedInput = HashPassword(password);
            return hashedInput.Equals(hashedPassword, StringComparison.OrdinalIgnoreCase);
        }
    }
}
