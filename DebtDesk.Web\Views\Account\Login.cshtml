@model DebtDesk.Web.Models.LoginViewModel
@{
    ViewData["Title"] = "Login";
    Layout = "~/Views/Shared/_LoginLayout.cshtml";
}

<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card">
            <div class="card-header">
                <h4 class="text-center">DebtDesk Login</h4>
            </div>
            <div class="card-body">
                <form asp-action="Login" method="post">
                    <div asp-validation-summary="All" class="text-danger"></div>
                    
                    <div class="mb-3">
                        <label asp-for="Username" class="form-label"></label>
                        <input asp-for="Username" class="form-control" />
                        <span asp-validation-for="Username" class="text-danger"></span>
                    </div>
                    
                    <div class="mb-3">
                        <label asp-for="Password" class="form-label"></label>
                        <input asp-for="Password" class="form-control" />
                        <span asp-validation-for="Password" class="text-danger"></span>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input asp-for="RememberMe" class="form-check-input" />
                        <label asp-for="RememberMe" class="form-check-label"></label>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Login</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
