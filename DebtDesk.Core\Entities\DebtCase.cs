using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DebtDesk.Core.Entities
{
    /// <summary>
    /// Represents a debt case (DLU - Debt case)
    /// States:
    /// 0 - import. Loaded into DB
    /// 10 - mail. Notification sent about new cases
    /// 20 - division. Assignment date and collection type recorded
    /// 30 - open. Assigned to collector
    /// 40 - control. Case under review
    /// 90 - close. Closed case
    /// 99 - delete. Deleted case
    /// </summary>
    public class DebtCase : BaseEntity
    {
        [MaxLength(50)]
        public string? DluEvCislo { get; set; }

        [MaxLength(20)]
        public string? IdDokladu { get; set; }

        [MaxLength(50)]
        public string? CisloSmlouvy { get; set; }

        public DateTime? DokladDvyst { get; set; }

        [MaxLength(200)]
        public string? PartnerNazev { get; set; }

        [MaxLength(20)]
        public string? PartnerRc { get; set; }

        [MaxLength(20)]
        public string? PartnerIc { get; set; }

        [MaxLength(50)]
        public string? PartnerPravniForma { get; set; }

        [MaxLength(20)]
        public string? PartnerMobil { get; set; }

        [MaxLength(50)]
        public string? PartnerEmail { get; set; }

        [MaxLength(30)]
        public string? NahradniTelefon { get; set; }

        [MaxLength(200)]
        public string? PoznamkaNahradniTel { get; set; }

        // Preferred address
        [MaxLength(200)]
        public string? PartnerUlicePref { get; set; }

        [MaxLength(200)]
        public string? PartnerMestoPref { get; set; }

        [MaxLength(20)]
        public string? PartnerPscPref { get; set; }

        [MaxLength(20)]
        public string? PartnerTel { get; set; }

        [MaxLength(20)]
        public string? PartnerMob { get; set; }

        // Correspondence address
        [MaxLength(200)]
        public string? PartnerUliceKores { get; set; }

        [MaxLength(200)]
        public string? PartnerMestoKores { get; set; }

        [MaxLength(20)]
        public string? PartnerPscKores { get; set; }

        [MaxLength(20)]
        public string? PartnerTel2 { get; set; }

        [MaxLength(20)]
        public string? PartnerMob2 { get; set; }

        // Authorized persons
        [MaxLength(200)]
        public string? OpravnenaOsoba1 { get; set; }

        [MaxLength(20)]
        public string? Oo1Rc { get; set; }

        [MaxLength(200)]
        public string? Oo1Ulice { get; set; }

        [MaxLength(200)]
        public string? Oo1Mesto { get; set; }

        [MaxLength(20)]
        public string? Oo1Psc { get; set; }

        [MaxLength(20)]
        public string? Oo1Tel { get; set; }

        [MaxLength(20)]
        public string? Oo1Mob { get; set; }

        [MaxLength(200)]
        public string? OpravnenaOsoba2 { get; set; }

        [MaxLength(20)]
        public string? Oo2Rc { get; set; }

        [MaxLength(200)]
        public string? Oo2Ulice { get; set; }

        [MaxLength(200)]
        public string? Oo2Mesto { get; set; }

        [MaxLength(20)]
        public string? Oo2Psc { get; set; }

        [MaxLength(20)]
        public string? Oo2Tel { get; set; }

        [MaxLength(20)]
        public string? Oo2Mob { get; set; }

        // Contract details
        [MaxLength(200)]
        public string? SmlouvaZajisteni { get; set; }

        [MaxLength(200)]
        public string? RucitelNazev { get; set; }

        [MaxLength(50)]
        public string? RucitelRcIc { get; set; }

        [MaxLength(200)]
        public string? RucitelUlice { get; set; }

        [MaxLength(200)]
        public string? RucitelMesto { get; set; }

        [MaxLength(20)]
        public string? RucitelPsc { get; set; }

        [MaxLength(20)]
        public string? RucitelTel { get; set; }

        [MaxLength(20)]
        public string? RucitelMob { get; set; }

        // Vehicle details
        [MaxLength(50)]
        public string? VozidloSpz { get; set; }

        [MaxLength(50)]
        public string? VozidloVin { get; set; }

        [MaxLength(200)]
        public string? VozidloZnacka { get; set; }

        [MaxLength(200)]
        public string? VozidloModel { get; set; }

        [MaxLength(50)]
        public string? VozidloRokVyroby { get; set; }

        [MaxLength(50)]
        public string? VozidloBarva { get; set; }

        [MaxLength(50)]
        public string? VozidloObjem { get; set; }

        [MaxLength(50)]
        public string? VozidloVykon { get; set; }

        [MaxLength(50)]
        public string? VozidloPalivo { get; set; }

        [MaxLength(50)]
        public string? VozidloKm { get; set; }

        [MaxLength(50)]
        public string? VozidloStav { get; set; }

        [MaxLength(50)]
        public string? VozidloCena { get; set; }

        [MaxLength(50)]
        public string? VozidloMisto { get; set; }

        [MaxLength(50)]
        public string? Tp { get; set; }

        [MaxLength(50)]
        public string? DatumVyrazeniTp { get; set; }

        [MaxLength(50)]
        public string? CisloTp { get; set; }

        [MaxLength(200)]
        public string? DodavatelNazev { get; set; }

        [MaxLength(50)]
        public string? DodavatelIc { get; set; }

        [MaxLength(100)]
        public string? Unused1 { get; set; }

        public DateTime? SmlouvaDpodpisu { get; set; }

        public DateTime? SmlouvaDukonceni { get; set; }

        public int? PocetSplatek { get; set; }

        [MaxLength(50)]
        public string? PredpisSplatky { get; set; }

        [MaxLength(50)]
        public string? SmlouvaSplatkaCastkaDluh { get; set; }

        public DateTime? SmlouvaDsplatky { get; set; }

        [MaxLength(50)]
        public string? DokladVs { get; set; }

        [MaxLength(50)]
        public string? MenaSmlouvy { get; set; }

        [MaxLength(50)]
        public string? PartnerAgentura { get; set; }

        public DateTime? DatumExportuNaAg { get; set; }

        public DateTime? DatumUkonceniVymahani { get; set; }

        [MaxLength(50)]
        public string? ZbpohlxxCastka { get; set; }

        public DateTime? VypovedDatum { get; set; }

        public byte Stav { get; set; } = 0;

        public byte Sent2User { get; set; } = 0;

        [MaxLength(20)]
        public string? PartnerDic { get; set; }

        public DateTime LastSk { get; set; } = new DateTime(2000, 1, 1);

        public DateTime? OrigDatumUkonceni { get; set; }

        public DateTime DmailProdlouzeni { get; set; } = new DateTime(2000, 1, 1);

        public DateTime? Dukonceni { get; set; }

        public float? CastkaKontrola { get; set; }

        public int KvStav { get; set; } = 1;

        public byte? Pojisteni { get; set; }

        // Foreign keys
        public int FileId { get; set; }
        public int? DebtTypeId { get; set; }
        public int? UserId { get; set; }

        // Navigation properties
        [ForeignKey("FileId")]
        public virtual File File { get; set; } = null!;

        [ForeignKey("DebtTypeId")]
        public virtual DebtType? DebtType { get; set; }

        [ForeignKey("UserId")]
        public virtual User? User { get; set; }

        public virtual ICollection<PaymentSchedule> PaymentSchedules { get; set; } = new List<PaymentSchedule>();
        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();
        public virtual ICollection<DebtCaseDocument> Documents { get; set; } = new List<DebtCaseDocument>();
        public virtual ICollection<Photo> Photos { get; set; } = new List<Photo>();
    }
}
