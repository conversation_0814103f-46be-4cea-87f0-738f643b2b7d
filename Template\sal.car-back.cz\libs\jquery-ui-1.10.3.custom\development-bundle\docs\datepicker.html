<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>jQuery UI datepicker documentation</title>

	<style>
	body {
		font-family: "Trebuchet MS", "Arial", "Helvetica", "Verdana", "sans-serif"
	}
	.gutter {
		display: none;
	}
	</style>
</head>
<body>

<script>{
		"title":
			"Datepicker Widget",
		"excerpt":
			"Select a date from a popup or inline calendar",
		"termSlugs": {
			"category": [
				"widgets"
			]
		}
	}</script><article id="datepicker1" class="entry widget"><h2 class="section-title">
<span>Datepicker Widget</span><span class="version-details">version added: 1.0</span>
</h2>
<div class="entry-wrapper">
<p class="desc"><strong>Description: </strong>Select a date from a popup or inline calendar</p>
<section id="quick-nav"><header><h2>QuickNav<a href="#entry-examples">Examples</a>
</h2></header><div class="quick-nav-section">
<h3>Options</h3>
<div><a href="#option-altField">altField</a></div>
<div><a href="#option-altFormat">altFormat</a></div>
<div><a href="#option-appendText">appendText</a></div>
<div><a href="#option-autoSize">autoSize</a></div>
<div><a href="#option-buttonImage">buttonImage</a></div>
<div><a href="#option-buttonImageOnly">buttonImageOnly</a></div>
<div><a href="#option-buttonText">buttonText</a></div>
<div><a href="#option-calculateWeek">calculateWeek</a></div>
<div><a href="#option-changeMonth">changeMonth</a></div>
<div><a href="#option-changeYear">changeYear</a></div>
<div><a href="#option-closeText">closeText</a></div>
<div><a href="#option-constrainInput">constrainInput</a></div>
<div><a href="#option-currentText">currentText</a></div>
<div><a href="#option-dateFormat">dateFormat</a></div>
<div><a href="#option-dayNames">dayNames</a></div>
<div><a href="#option-dayNamesMin">dayNamesMin</a></div>
<div><a href="#option-dayNamesShort">dayNamesShort</a></div>
<div><a href="#option-defaultDate">defaultDate</a></div>
<div><a href="#option-duration">duration</a></div>
<div><a href="#option-firstDay">firstDay</a></div>
<div><a href="#option-gotoCurrent">gotoCurrent</a></div>
<div><a href="#option-hideIfNoPrevNext">hideIfNoPrevNext</a></div>
<div><a href="#option-isRTL">isRTL</a></div>
<div><a href="#option-maxDate">maxDate</a></div>
<div><a href="#option-minDate">minDate</a></div>
<div><a href="#option-monthNames">monthNames</a></div>
<div><a href="#option-monthNamesShort">monthNamesShort</a></div>
<div><a href="#option-navigationAsDateFormat">navigationAsDateFormat</a></div>
<div><a href="#option-nextText">nextText</a></div>
<div><a href="#option-numberOfMonths">numberOfMonths</a></div>
<div><a href="#option-prevText">prevText</a></div>
<div><a href="#option-selectOtherMonths">selectOtherMonths</a></div>
<div><a href="#option-shortYearCutoff">shortYearCutoff</a></div>
<div><a href="#option-showAnim">showAnim</a></div>
<div><a href="#option-showButtonPanel">showButtonPanel</a></div>
<div><a href="#option-showCurrentAtPos">showCurrentAtPos</a></div>
<div><a href="#option-showMonthAfterYear">showMonthAfterYear</a></div>
<div><a href="#option-showOn">showOn</a></div>
<div><a href="#option-showOptions">showOptions</a></div>
<div><a href="#option-showOtherMonths">showOtherMonths</a></div>
<div><a href="#option-showWeek">showWeek</a></div>
<div><a href="#option-stepMonths">stepMonths</a></div>
<div><a href="#option-weekHeader">weekHeader</a></div>
<div><a href="#option-yearRange">yearRange</a></div>
<div><a href="#option-yearSuffix">yearSuffix</a></div>
<div><a href="#option-beforeShow">beforeShow</a></div>
<div><a href="#option-beforeShowDay">beforeShowDay</a></div>
<div><a href="#option-onChangeMonthYear">onChangeMonthYear</a></div>
<div><a href="#option-onClose">onClose</a></div>
<div><a href="#option-onSelect">onSelect</a></div>
</div>
<div class="quick-nav-section">
<h3>Methods</h3>
<div><a href="#method-destroy">destroy</a></div>
<div><a href="#method-dialog">dialog</a></div>
<div><a href="#method-isDisabled">isDisabled</a></div>
<div><a href="#method-hide">hide</a></div>
<div><a href="#method-show">show</a></div>
<div><a href="#method-refresh">refresh</a></div>
<div><a href="#method-getDate">getDate</a></div>
<div><a href="#method-setDate">setDate</a></div>
<div><a href="#method-option">option</a></div>
<div><a href="#method-widget">widget</a></div>
</div>
<div class="quick-nav-section"><h3>Events</h3></div></section><div class="longdesc" id="entry-longdesc">
		<p>The jQuery UI Datepicker is a highly configurable plugin that adds datepicker functionality to your pages. You can customize the date format and language, restrict the selectable date ranges and add in buttons and other navigation options easily.</p>

		<p>By default, the datepicker calendar opens in a small overlay when the associated text field gains focus. For an inline calendar, simply attach the datepicker to a div or span.</p>

		<h3>Keyboard interaction</h3>
		<p>While the datepicker is open, the following key commands are available:</p>
		<ul>
			<li>PAGE UP: Move to the previous month.</li>
			<li>PAGE DOWN: Move to the next month.</li>
			<li>CTRL+PAGE UP: Move to the previous year.</li>
			<li>CTRL+PAGE DOWN: Move to the next year.</li>
			<li>CTRL+HOME: Move to the current month. Open the datepicker if closed.</li>
			<li>CTRL+LEFT: Move to the previous day.</li>
			<li>CTRL+RIGHT: Move to the next day.</li>
			<li>CTRL+UP: Move to the previous week.</li>
			<li>CTRL+DOWN: Move the next week.</li>
			<li>ENTER: Select the focused date.</li>
			<li>CTRL+END: Close the datepicker and erase the date.</li>
			<li>ESCAPE: Close the datepicker without selection.</li>
		</ul>

		<h3 id="utility-functions">Utility functions</h3>
		<ul>
			<li>$.datepicker.setDefaults( settings ) - Set settings for all datepicker instances.</li>
			<li>$.datepicker.formatDate( format, date, settings ) - Format a date into a string value with a specified format.</li>
			<li>$.datepicker.parseDate( format, value, settings )  - Extract a date from a string value with a specified format.</li>
			<li>$.datepicker.iso8601Week( date ) - Determine the week of the year for a given date: 1 to 53.</li>
			<li>$.datepicker.noWeekends - Set as beforeShowDay function to prevent selection of weekends.</li>
		</ul>

		<h3>Localization</h3>
		<p>Datepicker provides support for localizing its content to cater for different languages and date formats. Each localization is contained within its own file with the language code appended to the name, e.g., <code>jquery.ui.datepicker-fr.js</code> for French. The desired localization file should be included after the main datepicker code. Each localization file adds its settings to the set of available localizations and automatically applies them as defaults for all instances.</p>
		<p>The <code>$.datepicker.regional</code> attribute holds an array of localizations, indexed by language code, with <code>""</code> referring to the default (English). Each entry is an object with the following attributes: <code>closeText</code>, <code>prevText</code>, <code>nextText</code>, <code>currentText</code>, <code>monthNames</code>, <code>monthNamesShort</code>, <code>dayNames</code>, <code>dayNamesShort</code>, <code>dayNamesMin</code>, <code>weekHeader</code>, <code>dateFormat</code>, <code>firstDay</code>, <code>isRTL</code>, <code>showMonthAfterYear</code>, and <code>yearSuffix</code>.</p>
		<p>You can restore the default localizations with:</p>
		<code>$.datepicker.setDefaults( $.datepicker.regional[ "" ] );</code>
		<p>And can then override an individual datepicker for a specific locale:</p>
		<code>$( selector ).datepicker( $.datepicker.regional[ "fr" ] );</code>
	</div>
<h3>Additional Notes:</h3>
<div class="longdesc"><ul><li>
			This widget requires some functional CSS, otherwise it won't work. If you build a custom theme, use the widget's specific CSS file as a starting point.
		</li></ul></div>
<section id="options"><header><h2 class="underline">Options</h2></header><div id="option-altField" class="api-item first-item">
<h3>altField<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Selector">Selector</a> or <a href="http://api.jquery.com/Types#jQuery">jQuery</a> or <a href="http://api.jquery.com/Types#Element">Element</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>""</code>
</div>
<div>An input element that is to be updated with the selected date from the datepicker. Use the <a href="#option-altFormat"><code>altFormat</code></a> option to change the format of the date within this field. Leave as blank for no alternate field.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the altField option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ altField: </code><code class="string">"#actualDate"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the altField option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">altField = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"altField"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"altField"</code><code class="plain">, </code><code class="string">"#actualDate"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-altFormat" class="api-item">
<h3>altFormat<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>""</code>
</div>
<div>The <a href="#option-dateFormat"><code>dateFormat</code></a> to be used for the <a href="#option-altField"><code>altField</code></a> option. This allows one date format to be shown to the user for selection purposes, while a different format is actually sent behind the scenes. For a full list of the possible formats see the [[UI/Datepicker/formatDate|formatDate]] function</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the altFormat option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ altFormat: </code><code class="string">"yy-mm-dd"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the altFormat option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">altFormat = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"altFormat"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"altFormat"</code><code class="plain">, </code><code class="string">"yy-mm-dd"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-appendText" class="api-item">
<h3>appendText<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>""</code>
</div>
<div>The text to display after each date field, e.g., to show the required format.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the appendText option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ appendText: </code><code class="string">"(yyyy-mm-dd)"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the appendText option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">appendText = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"appendText"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"appendText"</code><code class="plain">, </code><code class="string">"(yyyy-mm-dd)"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-autoSize" class="api-item">
<h3>autoSize<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Set to <code>true</code> to automatically resize the input field to accommodate dates in the current <a href="#option-dateFormat"><code>dateFormat</code></a>.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the autoSize option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ autoSize: </code><code class="keyword">true</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the autoSize option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">autoSize = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"autoSize"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"autoSize"</code><code class="plain">, </code><code class="keyword">true</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-beforeShow" class="api-item">
<h3>beforeShow<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types/#Function">Function</a>( <a href="http://api.jquery.com/Types#Element">Element</a> input, <a href="http://api.jquery.com/Types#Object">Object</a> inst )</span>
</h3>
<div class="default">
<strong>Default: </strong><code>null</code>
</div>
<div>A function that takes an input field and current datepicker instance and returns an options object to update the datepicker with. It is called just before the datepicker is displayed.</div>
</div>
<div id="option-beforeShowDay" class="api-item">
<h3>beforeShowDay<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types/#Function">Function</a>( <a href="http://api.jquery.com/Types#Date">Date</a> date )</span>
</h3>
<div class="default">
<strong>Default: </strong><code>null</code>
</div>
<div>A function takes a date as a parameter and must return an array with <code>[0]</code> equal to <code>true</code>/<code>false</code> indicating whether or not this date is selectable, <code>[1]</code> equal to a CSS class name or <code>""</code> for the default presentation, and <code>[2]</code> an optional popup tooltip for this date. It is called for each day in the datepicker before it is displayed.</div>
</div>
<div id="option-buttonImage" class="api-item">
<h3>buttonImage<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>""</code>
</div>
<div>The URL for the popup button image. If set, the <a href="#option-buttonText"><code>buttonText</code></a> option becomes the <code>alt</code> value and is not directly displayed.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the buttonImage option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ buttonImage: </code><code class="string">"/images/datepicker.gif"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the buttonImage option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">buttonImage = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"buttonImage"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"buttonImage"</code><code class="plain">, </code><code class="string">"/images/datepicker.gif"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-buttonImageOnly" class="api-item">
<h3>buttonImageOnly<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Whether the button image should be rendered by itself instead of inside a button element.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the buttonImageOnly option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ buttonImageOnly: </code><code class="keyword">true</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the buttonImageOnly option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">buttonImageOnly = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"buttonImageOnly"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"buttonImageOnly"</code><code class="plain">, </code><code class="keyword">true</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-buttonText" class="api-item">
<h3>buttonText<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>"..."</code>
</div>
<div>The text to display on the trigger button. Use in conjunction with the <a href="#option-showOn"><code>showOn</code></a> option set to <code>"button"</code> or <code>"both"</code>.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the buttonText option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ buttonText: </code><code class="string">"Choose"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the buttonText option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">buttonText = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"buttonText"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"buttonText"</code><code class="plain">, </code><code class="string">"Choose"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-calculateWeek" class="api-item">
<h3>calculateWeek<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types/#Function">Function</a>()</span>
</h3>
<div class="default">
<strong>Default: </strong><code>jQuery.datepicker.iso8601Week</code>
</div>
<div>A function to calculate the week of the year for a given date. The default implementation uses the ISO 8601 definition: weeks start on a Monday; the first week of the year contains the first Thursday of the year.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the calculateWeek option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ calculateWeek: myWeekCalc });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the calculateWeek option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">calculateWeek = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"calculateWeek"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"calculateWeek"</code><code class="plain">, myWeekCalc );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-changeMonth" class="api-item">
<h3>changeMonth<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Whether the month should be rendered as a dropdown instead of text.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the changeMonth option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ changeMonth: </code><code class="keyword">true</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the changeMonth option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">changeMonth = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"changeMonth"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"changeMonth"</code><code class="plain">, </code><code class="keyword">true</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-changeYear" class="api-item">
<h3>changeYear<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Whether the year should be rendered as a dropdown instead of text. Use the <a href="#option-yearRange"><code>yearRange</code></a> option to control which years are made available for selection.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the changeYear option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ changeYear: </code><code class="keyword">true</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the changeYear option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">changeYear = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"changeYear"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"changeYear"</code><code class="plain">, </code><code class="keyword">true</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-closeText" class="api-item">
<h3>closeText<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>"Done"</code>
</div>
<div>The text to display for the close link. Use the <a href="#option-showButtonPanel"><code>showButtonPanel</code></a> option to display this button.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the closeText option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ closeText: </code><code class="string">"Close"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the closeText option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">closeText = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"closeText"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"closeText"</code><code class="plain">, </code><code class="string">"Close"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-constrainInput" class="api-item">
<h3>constrainInput<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>true</code>
</div>
<div>When <code>true</code>, entry in the input field is constrained to those characters allowed by the current <a href="#option-dateFormat"><code>dateFormat</code></a> option.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the constrainInput option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ constrainInput: </code><code class="keyword">false</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the constrainInput option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">constrainInput = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"constrainInput"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"constrainInput"</code><code class="plain">, </code><code class="keyword">false</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-currentText" class="api-item">
<h3>currentText<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>"Today"</code>
</div>
<div>The text to display for the current day link. Use the <a href="#option-showButtonPanel"><code>showButtonPanel</code></a> option to display this button.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the currentText option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ currentText: </code><code class="string">"Now"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the currentText option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">currentText = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"currentText"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"currentText"</code><code class="plain">, </code><code class="string">"Now"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-dateFormat" class="api-item">
<h3>dateFormat<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>"mm/dd/yy"</code>
</div>
<div>The format for parsed and displayed dates. For a full list of the possible formats see the <code>[[UI/Datepicker/formatDate|formatDate]]</code> function.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the dateFormat option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ dateFormat: </code><code class="string">"yy-mm-dd"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the dateFormat option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">dateFormat = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"dateFormat"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"dateFormat"</code><code class="plain">, </code><code class="string">"yy-mm-dd"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-dayNames" class="api-item">
<h3>dayNames<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Array">Array</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>[ "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday" ]</code>
</div>
<div>The list of long day names, starting from Sunday, for use as requested via the <a href="#option-dateFormat"><code>dateFormat</code></a> option.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the dayNames option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ dayNames: [ </code><code class="string">"Dimanche"</code><code class="plain">, </code><code class="string">"Lundi"</code><code class="plain">, </code><code class="string">"Mardi"</code><code class="plain">, </code><code class="string">"Mercredi"</code><code class="plain">, </code><code class="string">"Jeudi"</code><code class="plain">, </code><code class="string">"Vendredi"</code><code class="plain">, </code><code class="string">"Samedi"</code> <code class="plain">] });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the dayNames option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">dayNames = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"dayNames"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"dayNames"</code><code class="plain">, [ </code><code class="string">"Dimanche"</code><code class="plain">, </code><code class="string">"Lundi"</code><code class="plain">, </code><code class="string">"Mardi"</code><code class="plain">, </code><code class="string">"Mercredi"</code><code class="plain">, </code><code class="string">"Jeudi"</code><code class="plain">, </code><code class="string">"Vendredi"</code><code class="plain">, </code><code class="string">"Samedi"</code> <code class="plain">] );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-dayNamesMin" class="api-item">
<h3>dayNamesMin<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Array">Array</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>[ "Su", "Mo", "Tu", "We", "Th", "Fr", "Sa" ]</code>
</div>
<div>The list of minimised day names, starting from Sunday, for use as column headers within the datepicker.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the dayNamesMin option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ dayNamesMin: [ </code><code class="string">"Di"</code><code class="plain">, </code><code class="string">"Lu"</code><code class="plain">, </code><code class="string">"Ma"</code><code class="plain">, </code><code class="string">"Me"</code><code class="plain">, </code><code class="string">"Je"</code><code class="plain">, </code><code class="string">"Ve"</code><code class="plain">, </code><code class="string">"Sa"</code> <code class="plain">] });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the dayNamesMin option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">dayNamesMin = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"dayNamesMin"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"dayNamesMin"</code><code class="plain">, [ </code><code class="string">"Di"</code><code class="plain">, </code><code class="string">"Lu"</code><code class="plain">, </code><code class="string">"Ma"</code><code class="plain">, </code><code class="string">"Me"</code><code class="plain">, </code><code class="string">"Je"</code><code class="plain">, </code><code class="string">"Ve"</code><code class="plain">, </code><code class="string">"Sa"</code> <code class="plain">] );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-dayNamesShort" class="api-item">
<h3>dayNamesShort<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Array">Array</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>[ "Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat" ]</code>
</div>
<div>The list of abbreviated day names, starting from Sunday, for use as requested via the <a href="#option-dateFormat"><code>dateFormat</code></a> option.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the dayNamesShort option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ dayNamesShort: [ </code><code class="string">"Dim"</code><code class="plain">, </code><code class="string">"Lun"</code><code class="plain">, </code><code class="string">"Mar"</code><code class="plain">, </code><code class="string">"Mer"</code><code class="plain">, </code><code class="string">"Jeu"</code><code class="plain">, </code><code class="string">"Ven"</code><code class="plain">, </code><code class="string">"Sam"</code> <code class="plain">] });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the dayNamesShort option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">dayNamesShort = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"dayNamesShort"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"dayNamesShort"</code><code class="plain">, [ </code><code class="string">"Dim"</code><code class="plain">, </code><code class="string">"Lun"</code><code class="plain">, </code><code class="string">"Mar"</code><code class="plain">, </code><code class="string">"Mer"</code><code class="plain">, </code><code class="string">"Jeu"</code><code class="plain">, </code><code class="string">"Ven"</code><code class="plain">, </code><code class="string">"Sam"</code> <code class="plain">] );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-defaultDate" class="api-item">
<h3>defaultDate<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Date">Date</a> or <a href="http://api.jquery.com/Types#Number">Number</a> or <a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>null</code>
</div>
<div>Set the date to highlight on first opening if the field is blank. Specify either an actual date via a Date object or as a string in the current <code>[[UI/Datepicker#option-dateFormat|dateFormat]]</code>, or a number of days from today (e.g. +7) or a string of values and periods ('y' for years, 'm' for months, 'w' for weeks, 'd' for days, e.g. '+1m +7d'), or null for today.</div>
<strong>Multiple types supported:</strong><ul>
<li>
<strong>Date</strong>: A date object containing the default date.</li>
<li>
<strong>Number</strong>: A number of days from today. For example <code>2</code> represents two days from today and <code>-1</code> represents yesterday.</li>
<li>
<strong>String</strong>: A string in the format defined by the <a href="#option-dateFormat"><code>dateFormat</code></a> option, or a relative date. Relative dates must contain value and period pairs; valid periods are <code>"y"</code> for years, <code>"m"</code> for months, <code>"w"</code> for weeks, and <code>"d"</code> for days. For example, <code>"+1m +7d"</code> represents one month and seven days from today.</li>
</ul>
<strong>Code examples:</strong><p>Initialize the datepicker with the defaultDate option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ defaultDate: +7 });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the defaultDate option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">defaultDate = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"defaultDate"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"defaultDate"</code><code class="plain">, +7 );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-duration" class="api-item">
<h3>duration<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#"></a> or <a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>"normal"</code>
</div>
<div>Control the speed at which the datepicker appears, it may be a time in milliseconds or a string representing one of the three predefined speeds ("slow", "normal", "fast").</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the duration option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ duration: </code><code class="string">"slow"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the duration option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">duration = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"duration"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"duration"</code><code class="plain">, </code><code class="string">"slow"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-firstDay" class="api-item">
<h3>firstDay<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Integer">Integer</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>0</code>
</div>
<div>Set the first day of the week: Sunday is <code>0</code>, Monday is <code>1</code>, etc.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the firstDay option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ firstDay: 1 });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the firstDay option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">firstDay = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"firstDay"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"firstDay"</code><code class="plain">, 1 );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-gotoCurrent" class="api-item">
<h3>gotoCurrent<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>When <code>true</code>, the current day link moves to the currently selected date instead of today.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the gotoCurrent option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ gotoCurrent: </code><code class="keyword">true</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the gotoCurrent option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">gotoCurrent = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"gotoCurrent"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"gotoCurrent"</code><code class="plain">, </code><code class="keyword">true</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-hideIfNoPrevNext" class="api-item">
<h3>hideIfNoPrevNext<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Normally the previous and next links are disabled when not applicable (see the <a href="#option-minDate"><code>minDate</code></a> and <a href="#option-maxDate"><code>maxDate</code></a> options). You can hide them altogether by setting this attribute to <code>true</code>.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the hideIfNoPrevNext option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ hideIfNoPrevNext: </code><code class="keyword">true</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the hideIfNoPrevNext option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">hideIfNoPrevNext = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"hideIfNoPrevNext"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"hideIfNoPrevNext"</code><code class="plain">, </code><code class="keyword">true</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-isRTL" class="api-item">
<h3>isRTL<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Whether the current language is drawn from right to left.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the isRTL option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ isRTL: </code><code class="keyword">true</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the isRTL option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">isRTL = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"isRTL"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"isRTL"</code><code class="plain">, </code><code class="keyword">true</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-maxDate" class="api-item">
<h3>maxDate<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Date">Date</a> or <a href="http://api.jquery.com/Types#Number">Number</a> or <a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>null</code>
</div>
<div>The maximum selectable date. When set to <code>null</code>, there is no maximum.</div>
<strong>Multiple types supported:</strong><ul>
<li>
<strong>Date</strong>: A date object containing the maximum date.</li>
<li>
<strong>Number</strong>: A number of days from today. For example <code>2</code> represents two days from today and <code>-1</code> represents yesterday.</li>
<li>
<strong>String</strong>: A string in the format defined by the <a href="#option-dateFormat"><code>dateFormat</code></a> option, or a relative date. Relative dates must contain value and period pairs; valid periods are <code>"y"</code> for years, <code>"m"</code> for months, <code>"w"</code> for weeks, and <code>"d"</code> for days. For example, <code>"+1m +7d"</code> represents one month and seven days from today.</li>
</ul>
<strong>Code examples:</strong><p>Initialize the datepicker with the maxDate option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ maxDate: </code><code class="string">"+1m +1w"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the maxDate option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">maxDate = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"maxDate"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"maxDate"</code><code class="plain">, </code><code class="string">"+1m +1w"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-minDate" class="api-item">
<h3>minDate<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Date">Date</a> or <a href="http://api.jquery.com/Types#Number">Number</a> or <a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>null</code>
</div>
<div>The minimum selectable date. When set to <code>null</code>, there is no minimum.</div>
<strong>Multiple types supported:</strong><ul>
<li>
<strong>Date</strong>: A date object containing the minimum date.</li>
<li>
<strong>Number</strong>: A number of days from today. For example <code>2</code> represents two days from today and <code>-1</code> represents yesterday.</li>
<li>
<strong>String</strong>: A string in the format defined by the <a href="#option-dateFormat"><code>dateFormat</code></a> option, or a relative date. Relative dates must contain value and period pairs; valid periods are <code>"y"</code> for years, <code>"m"</code> for months, <code>"w"</code> for weeks, and <code>"d"</code> for days. For example, <code>"+1m +7d"</code> represents one month and seven days from today.</li>
</ul>
<strong>Code examples:</strong><p>Initialize the datepicker with the minDate option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ minDate: </code><code class="keyword">new</code> <code class="plain">Date(2007, 1 - 1, 1) });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the minDate option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">minDate = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"minDate"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"minDate"</code><code class="plain">, </code><code class="keyword">new</code> <code class="plain">Date(2007, 1 - 1, 1) );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-monthNames" class="api-item">
<h3>monthNames<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Array">Array</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>[ "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December" ]</code>
</div>
<div>The list of full month names, for use as requested via the <a href="#option-dateFormat"><code>dateFormat</code></a> option.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the monthNames option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ monthNames: [ </code><code class="string">"Januar"</code><code class="plain">, </code><code class="string">"Februar"</code><code class="plain">, </code><code class="string">"Marts"</code><code class="plain">, </code><code class="string">"April"</code><code class="plain">, </code><code class="string">"Maj"</code><code class="plain">, </code><code class="string">"Juni"</code><code class="plain">, </code><code class="string">"Juli"</code><code class="plain">, </code><code class="string">"August"</code><code class="plain">, </code><code class="string">"September"</code><code class="plain">, </code><code class="string">"Oktober"</code><code class="plain">, </code><code class="string">"November"</code><code class="plain">, </code><code class="string">"December"</code> <code class="plain">] });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the monthNames option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">monthNames = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"monthNames"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"monthNames"</code><code class="plain">, [ </code><code class="string">"Januar"</code><code class="plain">, </code><code class="string">"Februar"</code><code class="plain">, </code><code class="string">"Marts"</code><code class="plain">, </code><code class="string">"April"</code><code class="plain">, </code><code class="string">"Maj"</code><code class="plain">, </code><code class="string">"Juni"</code><code class="plain">, </code><code class="string">"Juli"</code><code class="plain">, </code><code class="string">"August"</code><code class="plain">, </code><code class="string">"September"</code><code class="plain">, </code><code class="string">"Oktober"</code><code class="plain">, </code><code class="string">"November"</code><code class="plain">, </code><code class="string">"December"</code> <code class="plain">] );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-monthNamesShort" class="api-item">
<h3>monthNamesShort<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Array">Array</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>[ "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec" ]</code>
</div>
<div>The list of abbreviated month names, as used in the month header on each datepicker and as requested via the <a href="#option-dateFormat"><code>dateFormat</code></a> option.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the monthNamesShort option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ monthNamesShort: [ </code><code class="string">"Jan"</code><code class="plain">, </code><code class="string">"Feb"</code><code class="plain">, </code><code class="string">"Mar"</code><code class="plain">, </code><code class="string">"Apr"</code><code class="plain">, </code><code class="string">"Maj"</code><code class="plain">, </code><code class="string">"Jun"</code><code class="plain">, </code><code class="string">"Jul"</code><code class="plain">, </code><code class="string">"Aug"</code><code class="plain">, </code><code class="string">"Sep"</code><code class="plain">, </code><code class="string">"Okt"</code><code class="plain">, </code><code class="string">"Nov"</code><code class="plain">, </code><code class="string">"Dec"</code> <code class="plain">] });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the monthNamesShort option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">monthNamesShort = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"monthNamesShort"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"monthNamesShort"</code><code class="plain">, [ </code><code class="string">"Jan"</code><code class="plain">, </code><code class="string">"Feb"</code><code class="plain">, </code><code class="string">"Mar"</code><code class="plain">, </code><code class="string">"Apr"</code><code class="plain">, </code><code class="string">"Maj"</code><code class="plain">, </code><code class="string">"Jun"</code><code class="plain">, </code><code class="string">"Jul"</code><code class="plain">, </code><code class="string">"Aug"</code><code class="plain">, </code><code class="string">"Sep"</code><code class="plain">, </code><code class="string">"Okt"</code><code class="plain">, </code><code class="string">"Nov"</code><code class="plain">, </code><code class="string">"Dec"</code> <code class="plain">] );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-navigationAsDateFormat" class="api-item">
<h3>navigationAsDateFormat<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Whether the <a href="#option-prevText"><code>prevText</code></a> and <a href="#option-nextText"><code>nextText</code></a> options should be parsed as dates by the <code>[[UI/Datepicker/formatDate|formatDate]]</code> function, allowing them to display the target month names for example.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the navigationAsDateFormat option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ navigationAsDateFormat: </code><code class="keyword">true</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the navigationAsDateFormat option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">navigationAsDateFormat = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"navigationAsDateFormat"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"navigationAsDateFormat"</code><code class="plain">, </code><code class="keyword">true</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-nextText" class="api-item">
<h3>nextText<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>"Next"</code>
</div>
<div>The text to display for the next month link. With the standard ThemeRoller styling, this value is replaced by an icon.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the nextText option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ nextText: </code><code class="string">"Later"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the nextText option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">nextText = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"nextText"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"nextText"</code><code class="plain">, </code><code class="string">"Later"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-numberOfMonths" class="api-item">
<h3>numberOfMonths<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Number">Number</a> or <a href="http://api.jquery.com/Types#Array">Array</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>1</code>
</div>
<div>The number of months to show at once.</div>
<strong>Multiple types supported:</strong><ul>
<li>
<strong>Number</strong>: The number of months to display in a single row.</li>
<li>
<strong>Array</strong>: An array defining the number of rows and columns to display.</li>
</ul>
<strong>Code examples:</strong><p>Initialize the datepicker with the numberOfMonths option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ numberOfMonths: [ 2, 3 ] });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the numberOfMonths option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">numberOfMonths = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"numberOfMonths"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"numberOfMonths"</code><code class="plain">, [ 2, 3 ] );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-onChangeMonthYear" class="api-item">
<h3>onChangeMonthYear<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types/#Function">Function</a>( <a href="http://api.jquery.com/Types#Integer">Integer</a> year, <a href="http://api.jquery.com/Types#Integer">Integer</a> month, <a href="http://api.jquery.com/Types#Object">Object</a> inst )</span>
</h3>
<div class="default">
<strong>Default: </strong><code>null</code>
</div>
<div>Called when the datepicker moves to a new month and/or year. The function receives the selected year, month (1-12), and the datepicker instance as parameters. <code>this</code> refers to the associated input field.</div>
</div>
<div id="option-onClose" class="api-item">
<h3>onClose<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types/#Function">Function</a>( <a href="http://api.jquery.com/Types#String">String</a> dateText, <a href="http://api.jquery.com/Types#Object">Object</a> inst )</span>
</h3>
<div class="default">
<strong>Default: </strong><code>null</code>
</div>
<div>Called when the datepicker is closed, whether or not a date is selected. The function receives the selected date as text (<code>""</code> if none) and the datepicker instance as parameters. <code>this</code> refers to the associated input field.</div>
</div>
<div id="option-onSelect" class="api-item">
<h3>onSelect<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types/#Function">Function</a>( <a href="http://api.jquery.com/Types#String">String</a> dateText, <a href="http://api.jquery.com/Types#Object">Object</a> inst )</span>
</h3>
<div class="default">
<strong>Default: </strong><code>null</code>
</div>
<div>Called when the datepicker is selected. The function receives the selected date as text and the datepicker instance as parameters. <code>this</code> refers to the associated input field.</div>
</div>
<div id="option-prevText" class="api-item">
<h3>prevText<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>"Prev"</code>
</div>
<div>The text to display for the previous month link. With the standard ThemeRoller styling, this value is replaced by an icon.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the prevText option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ prevText: </code><code class="string">"Earlier"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the prevText option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">prevText = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"prevText"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"prevText"</code><code class="plain">, </code><code class="string">"Earlier"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-selectOtherMonths" class="api-item">
<h3>selectOtherMonths<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Whether days in other months shown before or after the current month are selectable. This only applies if the <a href="#option-showOtherMonths"><code>showOtherMonths</code></a> option is set to <code>true</code>.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the selectOtherMonths option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ selectOtherMonths: </code><code class="keyword">true</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the selectOtherMonths option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">selectOtherMonths = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"selectOtherMonths"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"selectOtherMonths"</code><code class="plain">, </code><code class="keyword">true</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-shortYearCutoff" class="api-item">
<h3>shortYearCutoff<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Number">Number</a> or <a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>"+10"</code>
</div>
<div>The cutoff year for determining the century for a date (used in conjunction with <code>[[UI/Datepicker#option-dateFormat|dateFormat]]</code> 'y'). Any dates entered with a year value less than or equal to the cutoff year are considered to be in the current century, while those greater than it are deemed to be in the previous century.</div>
<strong>Multiple types supported:</strong><ul>
<li>
<strong>Number</strong>: A value between <code>0</code> and <code>99</code> indicating the cutoff year.</li>
<li>
<strong>String</strong>: A relative number of years from the current year, e.g., <code>"+3"</code> or <code>"-5"</code>.</li>
</ul>
<strong>Code examples:</strong><p>Initialize the datepicker with the shortYearCutoff option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ shortYearCutoff: 50 });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the shortYearCutoff option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">shortYearCutoff = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"shortYearCutoff"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"shortYearCutoff"</code><code class="plain">, 50 );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-showAnim" class="api-item">
<h3>showAnim<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>"show"</code>
</div>
<div>The name of the animation used to show and hide the datepicker. Use <code>"show"</code> (the default), <code>"slideDown"</code>, <code>"fadeIn"</code>, any of the <a href="/category/effects/">jQuery UI effects</a>. Set to an empty string to disable animation.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the showAnim option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ showAnim: </code><code class="string">"fold"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the showAnim option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">showAnim = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"showAnim"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"showAnim"</code><code class="plain">, </code><code class="string">"fold"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-showButtonPanel" class="api-item">
<h3>showButtonPanel<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Whether to show the button panel.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the showButtonPanel option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ showButtonPanel: </code><code class="keyword">true</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the showButtonPanel option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">showButtonPanel = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"showButtonPanel"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"showButtonPanel"</code><code class="plain">, </code><code class="keyword">true</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-showCurrentAtPos" class="api-item">
<h3>showCurrentAtPos<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Number">Number</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>0</code>
</div>
<div>When displaying multiple months via the <a href="#option-numberOfMonths"><code>numberOfMonths</code></a> option, the <code>showCurrentAtPos</code> option defines which position to display the current month in.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the showCurrentAtPos option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ showCurrentAtPos: 3 });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the showCurrentAtPos option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">showCurrentAtPos = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"showCurrentAtPos"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"showCurrentAtPos"</code><code class="plain">, 3 );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-showMonthAfterYear" class="api-item">
<h3>showMonthAfterYear<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Whether to show the month after the year in the header.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the showMonthAfterYear option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ showMonthAfterYear: </code><code class="keyword">true</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the showMonthAfterYear option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">showMonthAfterYear = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"showMonthAfterYear"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"showMonthAfterYear"</code><code class="plain">, </code><code class="keyword">true</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-showOn" class="api-item">
<h3>showOn<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>"focus"</code>
</div>
<div>When the datepicker should appear. The datepicker can appear when the field receives focus (<code>"focus"</code>), when a button is clicked (<code>"button"</code>), or when either event occurs (<code>"both"</code>).</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the showOn option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ showOn: </code><code class="string">"both"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the showOn option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">showOn = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"showOn"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"showOn"</code><code class="plain">, </code><code class="string">"both"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-showOptions" class="api-item">
<h3>showOptions<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Object">Object</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>{}</code>
</div>
<div>If using one of the jQuery UI effects for the <a href="#option-showAnim"><code>showAnim</code></a> option, you can provide additional settings for that animation via this option.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the showOptions option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ showOptions: { direction: </code><code class="string">"up"</code> <code class="plain">} });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the showOptions option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">showOptions = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"showOptions"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"showOptions"</code><code class="plain">, { direction: </code><code class="string">"up"</code> <code class="plain">} );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-showOtherMonths" class="api-item">
<h3>showOtherMonths<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>Whether to display dates in other months (non-selectable) at the start or end of the current month. To make these days selectable use the <a href="#option-selectOtherMonths"><code>selectOtherMonths</code></a> option.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the showOtherMonths option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ showOtherMonths: </code><code class="keyword">true</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the showOtherMonths option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">showOtherMonths = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"showOtherMonths"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"showOtherMonths"</code><code class="plain">, </code><code class="keyword">true</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-showWeek" class="api-item">
<h3>showWeek<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>false</code>
</div>
<div>When <code>true</code>, a column is added to show the week of the year. The <a href="#option-calculateWeek"><code>calculateWeek</code></a> option determines how the week of the year is calculated. You may also want to change the <a href="#option-firstDay"><code>firstDay</code></a> option.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the showWeek option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ showWeek: </code><code class="keyword">true</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the showWeek option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">showWeek = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"showWeek"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"showWeek"</code><code class="plain">, </code><code class="keyword">true</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-stepMonths" class="api-item">
<h3>stepMonths<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#Number">Number</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>1</code>
</div>
<div>Set how many months to move when clicking the previous/next links.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the stepMonths option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ stepMonths: 3 });</code></div></div></td></tr></tbody></table></div>
<p>Get or set the stepMonths option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">stepMonths = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"stepMonths"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"stepMonths"</code><code class="plain">, 3 );</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-weekHeader" class="api-item">
<h3>weekHeader<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>"Wk"</code>
</div>
<div>The text to display for the week of the year column heading. Use the <a href="#option-showWeek"><code>showWeek</code></a> option to display this column.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the weekHeader option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ weekHeader: </code><code class="string">"W"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the weekHeader option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">weekHeader = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"weekHeader"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"weekHeader"</code><code class="plain">, </code><code class="string">"W"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-yearRange" class="api-item">
<h3>yearRange<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>"c-10:c+10"</code>
</div>
<div>The range of years displayed in the year drop-down: either relative to today's year (<code>"-nn:+nn"</code>), relative to the currently selected year (<code>"c-nn:c+nn"</code>), absolute (<code>"nnnn:nnnn"</code>), or combinations of these formats (<code>"nnnn:-nn"</code>). Note that this option only affects what appears in the drop-down, to restrict which dates may be selected use the <a href="#option-minDate"><code>minDate</code></a> and/or <a href="#option-maxDate"><code>maxDate</code></a> options.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the yearRange option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ yearRange: </code><code class="string">"2002:2012"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the yearRange option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">yearRange = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"yearRange"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"yearRange"</code><code class="plain">, </code><code class="string">"2002:2012"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
<div id="option-yearSuffix" class="api-item">
<h3>yearSuffix<span class="option-type"><strong>Type: </strong><a href="http://api.jquery.com/Types#String">String</a></span>
</h3>
<div class="default">
<strong>Default: </strong><code>""</code>
</div>
<div>Additional text to display after the year in the month headers.</div>
<strong>Code examples:</strong><p>Initialize the datepicker with the yearSuffix option specified:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker({ yearSuffix: </code><code class="string">"CE"</code> <code class="plain">});</code></div></div></td></tr></tbody></table></div>
<p>Get or set the yearSuffix option, after initialization:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="comments">// getter</code></div><div class="line number2 index1 alt1"><code class="keyword">var</code> <code class="plain">yearSuffix = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"yearSuffix"</code> <code class="plain">);</code></div><div class="line number3 index2 alt2">&nbsp;</div><div class="line number4 index3 alt1"><code class="comments">// setter</code></div><div class="line number5 index4 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"yearSuffix"</code><code class="plain">, </code><code class="string">"CE"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div></section><section id="methods"><header><h2 class="underline">Methods</h2></header><div id="method-destroy"><div class="api-item first-item">
<h3>destroy()</h3>
<div>
		Removes the datepicker functionality completely. This will return the element back to its pre-init state.
	</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the destroy method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"destroy"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-dialog"><div class="api-item">
<h3>dialog( date [, onSelect ] [, settings ] [, pos ] )</h3>
<div>Opens the datepicker in a dialog box.</div>
<ul>
<li>
<div><strong>date</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#String">String</a> or <a href="http://api.jquery.com/Types#Date">Date</a>
</div>
<div>The initial date.</div>
</li>
<li>
<div><strong>onSelect</strong></div>
<div>Type: <a href="http://api.jquery.com/Types/#Function">Function</a>()</div>
<div>A callback function when a date is selected. The function receives the date text and date picker instance as parameters.</div>
</li>
<li>
<div><strong>settings</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Options">Options</a>
</div>
<div>The new settings for the date picker.</div>
</li>
<li>
<div><strong>pos</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Number%5B2%5D%20or%20MouseEvent">Number[2] or MouseEvent</a>
</div>
<div>The position of the top/left of the dialog as <code>[x, y]</code> or a <code>MouseEvent</code> that contains the coordinates. If not specified the dialog is centered on the screen.</div>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Invoke the dialog method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"dialog"</code><code class="plain">, </code><code class="string">"10/12/2012"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-getDate"><div class="api-item">
<h3>getDate()<span class="returns">Returns: <a href="http://api.jquery.com/Types#Date">Date</a></span>
</h3>
<div>Returns the current date for the datepicker or <code>null</code> if no date has been selected.</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the getDate method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="keyword">var</code> <code class="plain">currentDate = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"getDate"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-hide"><div class="api-item">
<h3>hide()</h3>
<div>Close a previously opened date picker.</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the hide method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"hide"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-isDisabled"><div class="api-item">
<h3>isDisabled()<span class="returns">Returns: <a href="http://api.jquery.com/Types#Boolean">Boolean</a></span>
</h3>
<div>Determine whether a date picker has been disabled.</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the isDisabled method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="keyword">var</code> <code class="plain">isDisabled = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"isDisabled"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-option">
<div class="api-item">
<h3>option( optionName )<span class="returns">Returns: <a href="http://api.jquery.com/Types#Object">Object</a></span>
</h3>
<div>Gets the value currently associated with the specified <code>optionName</code>.</div>
<ul><li>
<div><strong>optionName</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#String">String</a>
</div>
<div>The name of the option to get.</div>
</li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the  method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="keyword">var</code> <code class="plain">isDisabled = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"disabled"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div class="api-item">
<h3>option()<span class="returns">Returns: <a href="http://api.jquery.com/Types#PlainObject">PlainObject</a></span>
</h3>
<div>Gets an object containing key/value pairs representing the current datepicker options hash.</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the  method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="keyword">var</code> <code class="plain">options = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div class="api-item">
<h3>option( optionName, value )</h3>
<div>Sets the value of the datepicker option associated with the specified <code>optionName</code>.</div>
<ul>
<li>
<div><strong>optionName</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#String">String</a>
</div>
<div>The name of the option to set.</div>
</li>
<li>
<div><strong>value</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>A value to set for the option.</div>
</li>
</ul>
<div>
<strong>Code examples:</strong><p>Invoke the  method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, </code><code class="string">"disabled"</code><code class="plain">, </code><code class="keyword">true</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div>
<div class="api-item">
<h3>option( options )</h3>
<div>Sets one or more options for the datepicker.</div>
<ul><li>
<div><strong>options</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#Object">Object</a>
</div>
<div>A map of option-value pairs to set.</div>
</li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the  method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"option"</code><code class="plain">, { disabled: </code><code class="keyword">true</code> <code class="plain">} );</code></div></div></td></tr></tbody></table></div>
</div>
</div>
</div>
<div id="method-refresh"><div class="api-item">
<h3>refresh()</h3>
<div>Redraw the date picker, after having made some external modifications.</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the refresh method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"refresh"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-setDate"><div class="api-item">
<h3>setDate( date )</h3>
<div>Sets the date for the datepicker. The new date may be a <code>Date</code> object or a string in the current <a href="#option-dateFormat">date format</a> (e.g., <code>"01/26/2009"</code>), a number of days from today (e.g., <code>+7</code>) or a string of values and periods (<code>"y"</code> for years, <code>"m"</code> for months, <code>"w"</code> for weeks, <code>"d"</code> for days, e.g., <code>"+1m +7d"</code>), or <code>null</code> to clear the selected date.</div>
<ul><li>
<div><strong>date</strong></div>
<div>Type: <a href="http://api.jquery.com/Types#String">String</a> or <a href="http://api.jquery.com/Types#Date">Date</a>
</div>
<div>The new date.</div>
</li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the setDate method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"setDate"</code><code class="plain">, </code><code class="string">"10/12/2012"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-show"><div class="api-item">
<h3>show()</h3>
<div>Open the date picker. If the datepicker is attached to an input, the input must be visible for the datepicker to be shown.</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the show method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">$( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"show"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div>
<div id="method-widget"><div class="api-item">
<h3>widget()<span class="returns">Returns: <a href="http://api.jquery.com/Types#jQuery">jQuery</a></span>
</h3>
<div>
		Returns a <code>jQuery</code> object containing the datepicker.
	</div>
<ul><li><div class="null-signature">This method does not accept any arguments.</div></li></ul>
<div>
<strong>Code examples:</strong><p>Invoke the widget method:</p>
<div class="syntaxhighlighter nogutter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="keyword">var</code> <code class="plain">widget = $( </code><code class="string">".selector"</code> <code class="plain">).datepicker( </code><code class="string">"widget"</code> <code class="plain">);</code></div></div></td></tr></tbody></table></div>
</div>
</div></div></section><section class="entry-examples" id="entry-examples"><header><h2 class="underline">Example:</h2></header><div class="entry-example" id="example-0">
<h4><span class="desc">A simple jQuery UI Datepicker.</span></h4>
<div class="syntaxhighlighter  "><table border="0" cellpadding="0" cellspacing="0"><tbody><tr><td class="gutter"><div class="line number1 index0 alt2">1</div><div class="line number2 index1 alt1">2</div><div class="line number3 index2 alt2">3</div><div class="line number4 index3 alt1">4</div><div class="line number5 index4 alt2">5</div><div class="line number6 index5 alt1">6</div><div class="line number7 index6 alt2">7</div><div class="line number8 index7 alt1">8</div><div class="line number9 index8 alt2">9</div><div class="line number10 index9 alt1">10</div><div class="line number11 index10 alt2">11</div><div class="line number12 index11 alt1">12</div><div class="line number13 index12 alt2">13</div><div class="line number14 index13 alt1">14</div><div class="line number15 index14 alt2">15</div><div class="line number16 index15 alt1">16</div><div class="line number17 index16 alt2">17</div><div class="line number18 index17 alt1">18</div><div class="line number19 index18 alt2">19</div></td><td class="code"><div class="container"><div class="line number1 index0 alt2"><code class="plain">&lt;!doctype html&gt;</code></div><div class="line number2 index1 alt1"><code class="plain">&lt;</code><code class="keyword">html</code> <code class="color1">lang</code><code class="plain">=</code><code class="string">"en"</code><code class="plain">&gt;</code></div><div class="line number3 index2 alt2"><code class="plain">&lt;</code><code class="keyword">head</code><code class="plain">&gt;</code></div><div class="line number4 index3 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">meta</code> <code class="color1">charset</code><code class="plain">=</code><code class="string">"utf-8"</code><code class="plain">&gt;</code></div><div class="line number5 index4 alt2"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">title</code><code class="plain">&gt;datepicker demo&lt;/</code><code class="keyword">title</code><code class="plain">&gt;</code></div><div class="line number6 index5 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">link</code> <code class="color1">rel</code><code class="plain">=</code><code class="string">"stylesheet"</code> <code class="color1">href</code><code class="plain">=</code><code class="string">"<a href="http://code.jquery.com/ui/1.9.2/themes/base/jquery-ui.css">http://code.jquery.com/ui/1.9.2/themes/base/jquery-ui.css</a>"</code><code class="plain">&gt;</code></div><div class="line number7 index6 alt2"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">script</code> <code class="color1">src</code><code class="plain">=</code><code class="string">"<a href="http://code.jquery.com/jquery-1.8.3.js">http://code.jquery.com/jquery-1.8.3.js</a>"</code><code class="plain">&gt;&lt;/</code><code class="keyword">script</code><code class="plain">&gt;</code></div><div class="line number8 index7 alt1"><code class="undefined spaces">&nbsp;&nbsp;&nbsp;&nbsp;</code><code class="plain">&lt;</code><code class="keyword">script</code> <code class="color1">src</code><code class="plain">=</code><code class="string">"<a href="http://code.jquery.com/ui/1.9.2/jquery-ui.js">http://code.jquery.com/ui/1.9.2/jquery-ui.js</a>"</code><code class="plain">&gt;&lt;/</code><code class="keyword">script</code><code class="plain">&gt;</code></div><div class="line number9 index8 alt2"><code class="plain">&lt;/</code><code class="keyword">head</code><code class="plain">&gt;</code></div><div class="line number10 index9 alt1"><code class="plain">&lt;</code><code class="keyword">body</code><code class="plain">&gt;</code></div><div class="line number11 index10 alt2">&nbsp;</div><div class="line number12 index11 alt1"><code class="plain">&lt;</code><code class="keyword">div</code> <code class="color1">id</code><code class="plain">=</code><code class="string">"datepicker"</code><code class="plain">&gt;&lt;/</code><code class="keyword">div</code><code class="plain">&gt;</code></div><div class="line number13 index12 alt2">&nbsp;</div><div class="line number14 index13 alt1"><code class="plain">&lt;</code><code class="keyword">script</code><code class="plain">&gt;</code></div><div class="line number15 index14 alt2"><code class="plain">$( "#datepicker" ).datepicker();</code></div><div class="line number16 index15 alt1"><code class="plain">&lt;/</code><code class="keyword">script</code><code class="plain">&gt;</code></div><div class="line number17 index16 alt2">&nbsp;</div><div class="line number18 index17 alt1"><code class="plain">&lt;/</code><code class="keyword">body</code><code class="plain">&gt;</code></div><div class="line number19 index18 alt2"><code class="plain">&lt;/</code><code class="keyword">html</code><code class="plain">&gt;</code></div></div></td></tr></tbody></table></div>
<h4>Demo:</h4>
<div class="demo code-demo" data-height="300"></div>
</div></section>
</div></article>

</body>
</html>
