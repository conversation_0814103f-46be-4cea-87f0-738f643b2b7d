<?php

/**
 * This file is part of the Grido (http://grido.bugyik.cz)
 *
 * Copyright (c) 2011 <PERSON><PERSON> (http://petr.bugyik.cz)
 *
 * For the full copyright and license information, please view
 * the file LICENSE.md that was distributed with this source code.
 */

namespace Grido\PropertyAccessors;

/**
 * @package     Grido
 * @subpackage  PropertyAccessors
 * <AUTHOR> <<EMAIL>>
 */
interface IPropertyAccessor
{
    /**
     * @param mixed $object
     * @param string $name
     * @return mixed
     */
    public static function getProperty($object, $name);

    /**
     * @param mixed $object
     * @param string $name
     * @param string $value
     * @return void
     */
    public static function setProperty($object, $name, $value);
}
