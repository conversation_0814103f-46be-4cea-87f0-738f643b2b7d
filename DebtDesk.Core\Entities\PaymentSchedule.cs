using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DebtDesk.Core.Entities
{
    /// <summary>
    /// Represents payment schedule (SK - Splatky/Payment schedules)
    /// </summary>
    public class PaymentSchedule : BaseEntity
    {
        [MaxLength(50)]
        public string? EvCislo { get; set; }

        [MaxLength(50)]
        public string? CisloSmlouvy { get; set; }

        [MaxLength(50)]
        public string? DluEvCislo { get; set; }

        public long? IdDokladu { get; set; }

        [MaxLength(200)]
        public string? PopisSplatky { get; set; }

        [MaxLength(20)]
        public string? PredpisSplatky { get; set; }

        public DateTime? DatumSplatnostiSplatky { get; set; }

        public DateTime? DatumUhrady { get; set; }

        [MaxLength(20)]
        public string? VyseUhradySplatky { get; set; }

        [MaxLength(20)]
        public string? VyseZustatku { get; set; }

        [MaxLength(50)]
        public string? TypSplatky { get; set; }

        [MaxLength(50)]
        public string? BankspojCislo { get; set; }

        [MaxLength(50)]
        public string? MenaFaktury { get; set; }

        // Foreign key
        public int FileId { get; set; }

        // Navigation properties
        [ForeignKey("FileId")]
        public virtual File File { get; set; } = null!;
    }
}
