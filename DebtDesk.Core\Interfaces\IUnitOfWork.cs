namespace DebtDesk.Core.Interfaces
{
    public interface IUnitOfWork : IDisposable
    {
        IDebtCaseRepository DebtCases { get; }
        IPaymentScheduleRepository PaymentSchedules { get; }
        IRepository<Entities.Payment> Payments { get; }
        IRepository<Entities.User> Users { get; }
        IRepository<Entities.File> Files { get; }
        IRepository<Entities.DebtType> DebtTypes { get; }
        IRepository<Entities.Photo> Photos { get; }
        IRepository<Entities.Review> Reviews { get; }
        IRepository<Entities.ReviewDocument> ReviewDocuments { get; }
        IRepository<Entities.DebtCaseDocument> DebtCaseDocuments { get; }

        Task<int> SaveChangesAsync();
        Task BeginTransactionAsync();
        Task CommitTransactionAsync();
        Task RollbackTransactionAsync();
    }
}
