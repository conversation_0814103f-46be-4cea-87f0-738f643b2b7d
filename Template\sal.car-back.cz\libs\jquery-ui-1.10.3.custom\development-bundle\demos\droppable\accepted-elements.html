<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>jQuery UI Droppable - Accept</title>
	<link rel="stylesheet" href="../../themes/base/jquery.ui.all.css">
	<script src="../../jquery-1.9.1.js"></script>
	<script src="../../ui/jquery.ui.core.js"></script>
	<script src="../../ui/jquery.ui.widget.js"></script>
	<script src="../../ui/jquery.ui.mouse.js"></script>
	<script src="../../ui/jquery.ui.draggable.js"></script>
	<script src="../../ui/jquery.ui.droppable.js"></script>
	<link rel="stylesheet" href="../demos.css">
	<style>
	#droppable { width: 150px; height: 150px; padding: 0.5em; float: left; margin: 10px; }
	#draggable, #draggable-nonvalid { width: 100px; height: 100px; padding: 0.5em; float: left; margin: 10px 10px 10px 0; }
	</style>
	<script>
	$(function() {
		$( "#draggable, #draggable-nonvalid" ).draggable();
		$( "#droppable" ).droppable({
			accept: "#draggable",
			activeClass: "ui-state-hover",
			hoverClass: "ui-state-active",
			drop: function( event, ui ) {
				$( this )
					.addClass( "ui-state-highlight" )
					.find( "p" )
						.html( "Dropped!" );
			}
		});
	});
	</script>
</head>
<body>

<div id="draggable-nonvalid" class="ui-widget-content">
	<p>I'm draggable but can't be dropped</p>
</div>

<div id="draggable" class="ui-widget-content">
	<p>Drag me to my target</p>
</div>

<div id="droppable" class="ui-widget-header">
	<p>accept: '#draggable'</p>
</div>

<div class="demo-description">
<p>Specify using the <code>accept</code> option which element (or group of elements) is accepted by the target droppable.</p>
</div>
</body>
</html>
