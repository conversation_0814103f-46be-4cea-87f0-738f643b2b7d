@model List<DebtDesk.Web.Models.DebtCaseViewModel>
@{
    ViewData["Title"] = "Seznam případů";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>@ViewData["Title"]</h2>
    <div>
        <a asp-action="New" class="btn btn-primary">Nové případy</a>
        <a asp-action="Division" class="btn btn-secondary">Rozdělení</a>
    </div>
</div>

@if (Model.Any())
{
    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead class="table-dark">
                <tr>
                    <th>Ev. č<PERSON>lo</th>
                    <th>Partner</th>
                    <th>Datum exportu</th>
                    <th>Datum ukončení</th>
                    <th>Stav</th>
                    <th>Přidělen</th>
                    <th>Typ</th>
                    <th>Akce</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model)
                {
                    <tr class="@(item.IsOverdue ? "table-danger" : item.IsWarning ? "table-warning" : "")">
                        <td>@item.DluEvCislo</td>
                        <td>@item.PartnerNazev</td>
                        <td>@(item.DatumExportuNaAg?.ToString("dd.MM.yyyy") ?? "-")</td>
                        <td>@(item.DatumUkonceniVymahani?.ToString("dd.MM.yyyy") ?? "-")</td>
                        <td>
                            <span class="@item.StatusCssClass">@item.StatusText</span>
                        </td>
                        <td>@item.UserName</td>
                        <td>@item.DebtTypeName</td>
                        <td>
                            <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-sm btn-outline-primary">Detail</a>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
}
else
{
    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i>
        Žádné případy k zobrazení.
    </div>
}

<div class="mt-4">
    <small class="text-muted">
        <span class="badge bg-danger me-2">■</span> Prošlé termíny
        <span class="badge bg-warning me-2">■</span> Blížící se termíny (do 10 dnů)
    </small>
</div>
