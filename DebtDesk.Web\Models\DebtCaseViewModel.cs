using DebtDesk.Core.Enums;

namespace DebtDesk.Web.Models
{
    public class DebtCaseViewModel
    {
        public int Id { get; set; }
        public string? DluEvCislo { get; set; }
        public string? PartnerNazev { get; set; }
        public string? PartnerRc { get; set; }
        public string? PartnerIc { get; set; }
        public string? PartnerEmail { get; set; }
        public string? PartnerMobil { get; set; }
        public string? PartnerUlicePref { get; set; }
        public string? PartnerMestoPref { get; set; }
        public string? PartnerPscPref { get; set; }
        public DateTime? DatumExportuNaAg { get; set; }
        public DateTime? DatumUkonceniVymahani { get; set; }
        public DebtCaseStatus Stav { get; set; }
        public string? UserName { get; set; }
        public string? DebtTypeName { get; set; }
        public string? FileName { get; set; }
        public int KvStav { get; set; }
        
        // Vehicle details
        public string? VozidloSpz { get; set; }
        public string? VozidloZnacka { get; set; }
        public string? VozidloModel { get; set; }
        public string? VozidloRokVyroby { get; set; }

        public string StatusText => Stav switch
        {
            DebtCaseStatus.Import => "Import",
            DebtCaseStatus.Mail => "Mail",
            DebtCaseStatus.Division => "Division",
            DebtCaseStatus.Open => "Open",
            DebtCaseStatus.Control => "Control",
            DebtCaseStatus.Close => "Closed",
            DebtCaseStatus.Delete => "Deleted",
            _ => "Unknown"
        };

        public string StatusCssClass => Stav switch
        {
            DebtCaseStatus.Import => "badge bg-secondary",
            DebtCaseStatus.Mail => "badge bg-info",
            DebtCaseStatus.Division => "badge bg-warning",
            DebtCaseStatus.Open => "badge bg-success",
            DebtCaseStatus.Control => "badge bg-primary",
            DebtCaseStatus.Close => "badge bg-dark",
            DebtCaseStatus.Delete => "badge bg-danger",
            _ => "badge bg-light"
        };

        public bool IsOverdue
        {
            get
            {
                if (DatumUkonceniVymahani == null) return false;
                return DateTime.Now > DatumUkonceniVymahani;
            }
        }

        public bool IsWarning
        {
            get
            {
                if (DatumUkonceniVymahani == null) return false;
                return DateTime.Now.AddDays(10) > DatumUkonceniVymahani && DateTime.Now <= DatumUkonceniVymahani;
            }
        }
    }

    public class PaymentScheduleViewModel
    {
        public int Id { get; set; }
        public string? PopisSplatky { get; set; }
        public DateTime? DatumSplatnostiSplatky { get; set; }
        public string? VyseUhradySplatky { get; set; }
        public string? VyseZustatku { get; set; }
        public string? TypSplatky { get; set; }

        public bool IsPaid => VyseZustatku == "0";
    }

    public class PaymentViewModel
    {
        public int Id { get; set; }
        public DateTime? DatumUhrady { get; set; }
        public string? CastkaUhradyMenaSmlouvy { get; set; }
        public byte Status { get; set; }
    }

    public class DebtCaseDetailsViewModel
    {
        public DebtCaseViewModel DebtCase { get; set; } = new();
        public List<PaymentScheduleViewModel> PaymentSchedules { get; set; } = new();
        public List<PaymentViewModel> Payments { get; set; } = new();
    }
}
