using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DebtDesk.Core.Entities
{
    /// <summary>
    /// Represents payment (UHR - Uhrady/Payments)
    /// </summary>
    public class Payment : BaseEntity
    {
        [MaxLength(50)]
        public string? EvCislo { get; set; }

        [MaxLength(50)]
        public string? CisloSmlouvy { get; set; }

        [MaxLength(50)]
        public string? DluEvCislo { get; set; }

        public long? IdDokladu { get; set; }

        [MaxLength(20)]
        public string? CastkaUhradyMenaSmlouvy { get; set; }

        public DateTime? DatumUhrady { get; set; }

        public DateTime? DatumPredaniAgenture { get; set; }

        public DateTime? DatumUkoneniVymahani { get; set; }

        [MaxLength(50)]
        public string? MenaFaktury { get; set; }

        public byte Status { get; set; } = 0;

        // Foreign key
        public int FileId { get; set; }

        // Navigation properties
        [ForeignKey("FileId")]
        public virtual File File { get; set; } = null!;
    }
}
