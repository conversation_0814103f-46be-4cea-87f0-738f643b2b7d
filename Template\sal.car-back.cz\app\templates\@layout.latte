{**
 * My Application layout template.
 *
 * @param string   $basePath web base path
 * @param string   $robots   tell robots how to index the content of a page (optional)
 * @param array    $flashes  flash messages
 *}

<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="description" content="">
	<meta name="robots" content="{$robots}" n:ifset="$robots">

	<title>{block title|striptags|upper}Nette Application Skeleton{/block}</title>

	<link rel="stylesheet" media="screen,projection,tv" href="{$basePath}/css/screen.css">
	<link rel="stylesheet" media="print" href="{$basePath}/css/print.css">
	<link rel="stylesheet" href="{$basePath}/css/grido.1.0.0.css">
	<link rel="shortcut icon" href="{$basePath}/favicon.ico">
	{block head}{/block}
</head>

<body>
    <div id="header">
        <div id="header-inner">
            <div>
                <img src="{$basePath}/images/LCSlogo2023.png" > 
            </div>
            {if $user->isLoggedIn()}
            <div class="user">
                <span class="menu">
                    {if $user->isInRole('NEWDLU')}
                    <a n:href="Pripady:new">Nové případy</a> |
                    <a n:href="Pripady:rozdeleni">Rozdělení případů</a> |
                    {/if}
                    {if $user->isInRole('DLU')}
                    <a n:href="Pripady:">Seznam případů</a>  |
                    <a n:href="Pripady:allusers">Všechny případy bez KV</a>  |
                    <a n:href="Pripady:ru">RU případy</a>  |
                    <a n:href="Pripady:kv">KV případy</a>  |
                    <a n:href="Pripady:close">Uzavřené případy</a> 
                    {/if}
                    {if $user->isInRole('REVIEW')}
                    | <a n:href="review:">Prověrky</a>
                    {/if}
                    {if $user->isInRole('TELPRIPADY')}
                    | <a n:href="Telpripady:">Tel. případy</a>
                    {/if}
                    {if $user->isInRole('PISPRIPADY')}
                    | <a n:href="Pispripady:">Pís. případy</a>
                    {/if}
                    {if $user->isInrole('ROOT')}
                        | <a n:href="Pripady:prodlouzeni">Prodlužování případů</a>
                    {/if}
                    {if $user->isInRole('ROOT')}
                        |
                    <a n:href="Import:">Import dat</a> |
                        <a n:href="Import:resetdb">Reset DB</a> |
                        <a n:href="Import:Wget, pass => CrujeTHe6E24aphefucr">WGET</a></div>
                    {/if}
                </span>
                <span class="tab"> </span>
                <span class="user">{$user->getIdentity()->name}</span> |
                <a n:href="User:password">Změna hesla</a> |
                <a n:href="signOut!">Odhlásit se</a>
            </div>
            {/if}
        </div>
    </div>

    <div id="container">

        <div id="content">
            <div n:foreach="$flashes as $flash" class="flash {$flash->type}">{$flash->message}</div>

            {include #content}
        </div>

        <div id="footer">

        </div>
    </div>

{ifset $dump}
{dump $dump}
{/ifset}

	{block scripts}
	<script src="{$basePath}/js/jquery.js"></script>
	<script src="{$basePath}/js/netteForms.js"></script>
	<script src="{$basePath}/js/main.js"></script>
        
        <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.4/jquery.min.js"></script>
        <script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.8/jquery-ui.min.js"></script>
        <link rel="stylesheet" media="screen,projection,tv"
            href="http://ajax.googleapis.com/ajax/libs/jqueryui/1.8/themes/smoothness/jquery-ui.css">
        
        <script>
        /* Czech initialisation for the jQuery UI date picker plugin. */
        /* Written by Tomas Muller (<EMAIL>). */
        jQuery(function($) {
            $.datepicker.regional['cs'] = {
                closeText: 'Zavřít',
                prevText: '&#x3c;Dříve',
                nextText: 'Později&#x3e;',
                currentText: 'Nyní',
                monthNames: ['leden', 'únor', 'březen', 'duben', 'květen', 'červen', 'červenec', 'srpen',
                    'září', 'říjen', 'listopad', 'prosinec'],
                monthNamesShort: ['led', 'úno', 'bře', 'dub', 'kvě', 'čer', 'čvc', 'srp', 'zář', 'říj', 'lis', 'pro'],
                dayNames: ['neděle', 'pondělí', 'úterý', 'středa', 'čtvrtek', 'pátek', 'sobota'],
                dayNamesShort: ['ne', 'po', 'út', 'st', 'čt', 'pá', 'so'],
                dayNamesMin: ['ne', 'po', 'út', 'st', 'čt', 'pá', 'so'],
                weekHeader: 'Týd',
                dateFormat: 'dd.mm.yy',
                firstDay: 1,
                isRTL: false,
                showMonthAfterYear: false,
                yearSuffix: ''
            };
            $.datepicker.setDefaults($.datepicker.regional['cs']);
        });
        </script>

        <script>
        $(document).ready(function () {
            $("input.date").each(function () { // input[type=date] does not work in IE
                var el = $(this);
                var value = el.val();
                var date = (value ? $.datepicker.parseDate($.datepicker.W3C, value) : null);

                var minDate = el.attr("min") || null;
                if (minDate) minDate = $.datepicker.parseDate($.datepicker.W3C, minDate);
                var maxDate = el.attr("max") || null;
                if (maxDate) maxDate = $.datepicker.parseDate($.datepicker.W3C, maxDate);

                // input.attr("type", "text") throws exception
                if (el.attr("type") == "date") {
                    var tmp = $("<input/>");
                    $.each("class,disabled,id,maxlength,name,readonly,required,size,style,tabindex,title,value".split(","), function(i, attr)  {
                        tmp.attr(attr, el.attr(attr));
                    });
                    tmp.data(el.data());
                    el.replaceWith(tmp);
                    el = tmp;
                }
                el.datepicker({
                    minDate: minDate,
                    maxDate: maxDate
                });
                el.val($.datepicker.formatDate(el.datepicker("option", "dateFormat"), date));
            });
        });
        </script>

{/block}
</body>
</html>
